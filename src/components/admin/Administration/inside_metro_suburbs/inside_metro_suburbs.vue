<template>
  <div class="inside-metro-suburbs-container">
    <TableTitleHeader>
      <template #title>
        <GTitle
          title="Metro Suburb"
          subtitle="Add or edit suburbs that are in metro areas. Used to determine if outside metro charges are applicable"
          :divider="false"
        />
      </template>
    </TableTitleHeader>

    <form-card class="mt-4">
      <div slot="heading">Inside Metro Suburb Maintenance</div>
      <div slot="subheading">
        Search and add new suburbs to the Division's Inside Metro Suburbs list
      </div>
      <div slot="top-right">
        <v-layout> </v-layout>
      </div>
      <div slot="content">
        <v-layout wrap>
          <v-flex md12>
            <AddressSearchAU
              :address="address"
              label="Full Address"
              :setFocus="true"
              :hideFullAddress="true"
              :suburbSearch="true"
              :enableManualAddress="false"
              :enableNicknamedAddress="false"
              :enablePinDrop="false"
              :enableReturnToDefaultDispatchAddress="false"
              :key="isAwaitingResponse"
              :formDisabled="!isAuthorised()"
            >
            </AddressSearchAU>
          </v-flex>
          <v-flex md12>
            <v-layout justify-end>
              <v-btn
                depressed
                :loading="isAwaitingResponse"
                color="blue"
                :disabled="
                  !address ||
                  !address.suburb ||
                  !address.postcode ||
                  !isAuthorised()
                "
                @click="saveInsideMetroSuburb"
              >
                Add Suburb
              </v-btn>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </form-card>

    <form-card color="orange">
      <div slot="heading">Inside Metro Suburbs List</div>
      <div slot="subheading">
        All areas that do not have a outside metro charge applied
      </div>
      <div slot="top-right">
        <v-layout> </v-layout>
      </div>
      <div slot="content">
        <v-flex md12>
          <v-text-field
            label="Search for Suburb"
            v-model="search"
            outline
            class="v-solo-custom"
            color="Orange"
          />
          <v-data-table
            :headers="headers"
            :search="search"
            :items="insideMetroSuburbs.suburbs"
            item-key="_id"
            class="gd-dark-theme"
            :rows-per-page-items="[15, 30]"
          >
            <template v-slot:items="props">
              <tr>
                <td>{{ props.item.name }}</td>
                <td>{{ props.item.postcode }}</td>
                <td class="text-xs-center" style="width: 400px">
                  <v-layout justify-center>
                    <ConfirmationDialog
                      buttonText="Remove Suburb"
                      :message="
                        'Please confirm that you wish to remove ' +
                        props.item.name +
                        ' (' +
                        props.item.postcode +
                        ') from your inside metro suburbs list.'
                      "
                      :isSmallButton="true"
                      :isOutlineButton="true"
                      faIconName="fa-trash"
                      title="Suburb removal confirmation"
                      @confirm="
                        removeSuburb(props.item.postcode, props.item.name)
                      "
                      :buttonDisabled="!isAuthorised()"
                      :buttonColor="'error'"
                      :isDepressedButton="true"
                      :confirmationButtonText="'Confirm'"
                      :isCheckbox="false"
                    />
                  </v-layout>
                </td>
              </tr>
            </template>
            <template v-slot:no-results>
              <v-alert
                class="v-alert-main"
                :value="true"
                color="error"
                icon="warning"
              >
                Your search for "{{ search }}" found no results.
              </v-alert>
            </template>
          </v-data-table>
        </v-flex>
      </div>
    </form-card>
  </div>
</template>

<script setup lang="ts">
import AddressSearchAU from '@/components/common/addressing/address-search-au/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import FormCard from '@/components/common/ui-elements/form_card.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import AddressAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import InsideMetroSuburb from '@/interface-models/OutsideMetro/InsideMetroSuburb';
import InsideSuburb from '@/interface-models/OutsideMetro/InsideSuburb';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { computed, ref, Ref } from 'vue';

const componentTitle: string = 'Inside Metro Suburbs';

const companyDetailsStore = useCompanyDetailsStore();

const address: Ref<AddressAU> = ref(new AddressAU());
const search: Ref<string> = ref('');

const isAwaitingResponse: Ref<boolean> = ref(false);

const headers: TableHeader[] = [
  {
    text: 'Suburb Name',
    align: 'left',
    sortable: true,
    value: 'name',
  },
  {
    text: 'Post code',
    align: 'left',
    sortable: false,
    value: 'postcode',
  },
  {
    text: 'Action',
    align: 'center',
    sortable: false,
    value: '',
  },
];

const insideMetroSuburbs = computed(() => {
  if (!companyDetailsStore.insideMetroSuburbs) {
    const insideMetro = new InsideMetroSuburb();
    return insideMetro;
  }
  return companyDetailsStore.insideMetroSuburbs;
});

/**
 * Removes the selected suburb from the insideMetroSuburbs list and updates
 * the full list. The match is done
 * @param postcode - The postcode of the suburb to be removed.
 * @param name - The name of the suburb to be removed.
 */
function removeSuburb(postcode: string, name: string) {
  const divisionSuburbs = JSON.parse(JSON.stringify(insideMetroSuburbs.value));
  const indexToBeRemoved = divisionSuburbs.suburbs.findIndex(
    (x: InsideSuburb) => x.postcode === postcode && x.name === name,
  );
  if (indexToBeRemoved !== -1) {
    divisionSuburbs.suburbs.splice(indexToBeRemoved, 1);
  }
  companyDetailsStore.saveInsideMetroSuburbs(divisionSuburbs);
}

/**
 * Adds the selected address to the insideMetroSuburbs list, then saves the
 * full document. If the suburb already exists, a notification is shown.
 */
async function saveInsideMetroSuburb() {
  let divisionSuburbs = JSON.parse(JSON.stringify(insideMetroSuburbs.value));
  if (!divisionSuburbs) {
    divisionSuburbs = new InsideMetroSuburb();
  }

  if (address.value.suburb !== '') {
    const alreadyExists = divisionSuburbs.suburbs.find(
      (x: InsideSuburb) =>
        x.postcode === address.value.postcode &&
        address.value.suburb.toLowerCase() === x.name.toLowerCase(),
    );
    if (alreadyExists) {
      showAppNotification(
        `The suburb you've entered already exists as an Inside Metro Suburb.`,
        HealthLevel.INFO,
      );
      return;
    }
    divisionSuburbs.suburbs.push(
      new InsideSuburb(address.value.suburb, address.value.postcode),
    );

    // Send request and handle response
    isAwaitingResponse.value = true;
    const result =
      await companyDetailsStore.saveInsideMetroSuburbs(divisionSuburbs);
    if (result) {
      showAppNotification('Suburb successfully added!', HealthLevel.SUCCESS);
      address.value = new AddressAU();
    } else {
      showAppNotification(GENERIC_ERROR_MESSAGE);
    }
    isAwaitingResponse.value = false;
  }
}

// Trigger app notification. Defaults to ERROR type message, but type can be
// provided to produce other types. Includes componentTitle as a title for the
// notification.
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: componentTitle,
  });
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>

<style scoped lang="scss">
.inside-metro-suburbs-container {
  padding-bottom: 58px;
}
</style>
