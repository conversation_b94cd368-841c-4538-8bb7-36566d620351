<template>
  <div class="driver-details-personal-container">
    <v-layout wrap>
      <v-flex md12>
        <RateExpirationSummaryAlert
          :expirationSummaries="driverDetails.allRateExpirationSummaries"
        ></RateExpirationSummaryAlert>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">
            {{
              !driverDetails.isGenericDriver
                ? `Driver Personal`
                : `Generic
          Driver`
            }}
            Information
          </h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <FormFieldRow label="Name:" :required="true">
        <v-text-field
          v-model="driverDetails.name"
          :disabled="!isEdited"
          solo
          flat
          label="Driver Name"
          :rules="[validate.required]"
          class="form-field-required v-solo-custom"
          autofocus
        />
      </FormFieldRow>
      <FormFieldRow
        label="Assigned ID:"
        :required="true"
        v-if="assignedIdRequired"
      >
        <v-text-field
          v-model="driverDetails.assignedId"
          :disabled="!isEdited"
          solo
          flat
          :rules="[validate.required, validateDriverAssignedId]"
          label="Assigned ID"
          class="form-field-required v-solo-custom"
          hint="This ID will be used across the app to identify this driver."
          autofocus
        />
      </FormFieldRow>
      <FormFieldRow label="Active Status:" :required="true">
        <v-layout>
          <StatusSelect
            :key="statusListKey"
            label="Secondary Status"
            :statusCategory="8"
            :boxInput="false"
            :isRequired="true"
            :soloInput="true"
            :validate="validate"
            :statusList="driverDetails.statusList"
            :resetSelectedSecondaryStatus="false"
            :formDisabled="true"
            :hintText="
              !allowUpdateOperationalStatus && !isEdited
                ? 'Please provide a mobile number before enabling this driver.'
                : undefined
            "
          >
          </StatusSelect>
          <SubcontractorUpdateOperationalStatus
            :isEdited="isEdited || !allowUpdateOperationalStatus"
            :entityId="driverDetails.driverId"
            :entityType="driverEntityType"
            :statusList="driverDetails.statusList"
          />
        </v-layout>
      </FormFieldRow>

      <!-- ------------------------------ -->
      <!-- INPUTS FOR NON-GENERIC DRIVERS -->
      <!-- ------------------------------ -->
      <template v-if="!driverDetails.isGenericDriver">
        <FormFieldRow label="Email Address:" :required="true">
          <v-layout>
            <div style="position: relative; width: 100%">
              <GEmailValidator
                v-model="driverDetails.email"
                :hideDetails="true"
                :isDriver="true"
                @setExistingDriver="setExistingDriverEmail"
                :disabled="
                  !isEdited ||
                  (!canEditUsername && driverDetails._id !== undefined)
                "
              />
              <div
                class="existing-driver-mobile-select-text"
                v-if="existingDriverByEmail"
                @click="setExistingDriverDialog(true, true)"
              >
                Did you mean {{ existingDriverByEmail.name }}?
              </div>
            </div>
            <DriverDetailsUpdateDetails
              v-if="!canEditUsername && driverDetails._id !== undefined"
              :firstName="driverDetails.name"
              :lastName="''"
              :emailAddress="driverDetails.email"
              :mobileNumber="driverDetails.mobile"
              :_id="driverDetails._id"
              :disabled="isEdited"
            />
          </v-layout>
        </FormFieldRow>
        <FormFieldRow
          label="Mobile Number:"
          :required="mobileNumberValidationRequired"
        >
          <v-layout>
            <div style="position: relative; width: 100%">
              <GMobileValidator
                ref="GMobileValidatorRef"
                v-model="driverDetails.mobile"
                :isDriver="true"
                :hideDetails="true"
                @setExistingDriverMobile="setExistingDriverMobile"
                :disabled="
                  !isEdited ||
                  (!canEditUsername && driverDetails._id !== undefined)
                "
              />
              <div
                class="existing-driver-mobile-select-text"
                v-if="existingDriverByMobile"
                @click="setExistingDriverDialog(true, false)"
              >
                Did you mean {{ existingDriverByMobile.name }}?
              </div>
            </div>
            <DriverDetailsUpdateDetails
              v-if="!canEditUsername && driverDetails._id !== undefined"
              :firstName="driverDetails.name"
              :lastName="''"
              :emailAddress="driverDetails.email"
              :mobileNumber="driverDetails.mobile"
              :disabled="isEdited"
              :_id="driverDetails._id"
            />
          </v-layout>
        </FormFieldRow>
        <FormFieldRow label="Alt. Contact Number:">
          <v-text-field
            :value="formatPhoneNumber(driverDetails.phoneNumber)"
            @input="driverDetails.phoneNumber = formatPhoneNumber($event)"
            :disabled="!isEdited"
            solo
            class="v-solo-custom"
            flat
          />
        </FormFieldRow>
        <FormFieldRow label="Home Address:">
          <AddressSearchAU
            :boxInput="false"
            :soloInput="true"
            :formDisabled="!isEdited"
            :address="driverDetails.address"
            :label="''"
            :enableNicknamedAddress="false"
            :enableSuburbSelect="false"
            :enableReturnToDefaultDispatchAddress="false"
          />
        </FormFieldRow>
        <FormFieldRow label="Date of Birth:">
          <DateTimeInputs
            :epochTime.sync="driverDetails.dateOfBirth"
            :enableValidation="true"
            :type="DateTimeType.DATE_START_OF_DAY"
            dateLabel="Date of Birth"
            :readOnly="!isEdited"
            :soloInput="true"
            :boxInput="false"
            :hintTextType="HintTextType.NONE"
          ></DateTimeInputs>
        </FormFieldRow>
        <FormFieldRow label="Police Check Date:">
          <DateTimeInputs
            :epochTime.sync="driverDetails.policeCheckDate"
            :enableValidation="true"
            :type="DateTimeType.DATE_START_OF_DAY"
            dateLabel="Police Check"
            :readOnly="!isEdited"
            :soloInput="true"
            :boxInput="false"
            :hintTextType="HintTextType.NONE"
          ></DateTimeInputs>
        </FormFieldRow>

        <FormFieldRow label="Employment Start Date:">
          <DateTimeInputs
            :epochTime.sync="driverDetails.dateStarted"
            :enableValidation="true"
            :type="DateTimeType.DATE_START_OF_DAY"
            dateLabel="Employment Start Date"
            :readOnly="!isEdited"
            :soloInput="true"
            :boxInput="false"
            :hintTextType="HintTextType.NONE"
          ></DateTimeInputs>
        </FormFieldRow>
        <FormFieldRow label="Date Finished:">
          <DateTimeInputs
            :epochTime.sync="driverDetails.dateFinished"
            :enableValidation="true"
            :type="DateTimeType.DATE_END_OF_DAY"
            dateLabel="Employment Start Date"
            :readOnly="!isEdited"
            :soloInput="true"
            :boxInput="false"
            :hintTextType="HintTextType.NONE"
          ></DateTimeInputs>
        </FormFieldRow>
        <v-flex md12 pb-3
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">Profile Image</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
        <FormFieldRow label="Driver Photo:">
          <FileUpload
            :imageLabel="'DRIVER PORTRAIT'"
            :attachmentSingle="true"
            :documentTypeId="attachmentTypes.DRIVER_PORTRAIT"
            :attachment="driverDetails.photo"
            :formDisabled="!isEdited"
            :isLoading.sync="isLoading"
          >
          </FileUpload>
        </FormFieldRow>
      </template>
      <v-flex md12 pb-3 v-if="isAuthorisedAdmin()"
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">Compliance Overrides</h5>
          <span
            class="accent-text--card warning-type px-2"
            style="color: black"
          >
            ADMIN ONLY
          </span>
          <v-flex px-2>
            <v-divider></v-divider>
          </v-flex>
          <span>
            <v-switch
              label="Show Advanced"
              v-model="showAdminComplianceSettings"
              :disabled="!isEdited"
            ></v-switch>
          </span>
        </v-layout>
        <v-layout v-show="showAdminComplianceSettings">
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Lock to Owner:</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <SelectEntity
                  :entityTypes="[entityType.FLEET_ASSET_OWNER]"
                  :id.sync="driverDetails.restrictedToOwnerId"
                  :disabled="!isEdited"
                  :hint="'Restrict Driver association to a single Fleet Asset Owner. This is used for Generic Drivers.'"
                  :placeholder="'Fleet Asset Owner Select'"
                  :isMongoId="true"
                />
              </v-flex>
            </v-layout>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">
                    Licence Compliance:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-checkbox
                  v-model="licenceComplianceRequired"
                  :disabled="!isEdited"
                  color="light-blue"
                  label="Licence Compliance Required"
                  hint="Compliance checks related to Licences will be carried out for this Driver. Overdue, expiring or missing Licences will appear on the Subcontractor Dashboard"
                  persistent-hint
                  class="mt-2"
                />
              </v-flex>
            </v-layout>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">
                    Induction Compliance:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-checkbox
                  v-model="inductionComplianceRequired"
                  :disabled="!isEdited"
                  color="light-blue"
                  label="Induction Compliance Required"
                  hint="Compliance checks related to Inductions will be carried out for this Driver. Overdue, expiring or missing Inductions will appear on the Subcontractor Dashboard"
                  persistent-hint
                  class="mt-2"
                />
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 v-if="isAuthorisedAdmin() && showAdminComplianceSettings">
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Mobile App:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-checkbox
              v-model="driverUsesMobileApp"
              :disabled="!isEdited"
              color="light-blue"
              label="Driver Uses Mobile App"
              hint="Will the driver use the mobile app to complete jobs? If not selected, jobs must be completed manually from the Operations dashboard."
              persistent-hint
              class="mt-2"
            />
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>

    <GDialog
      :name="'GDialog'"
      :width="'500px'"
      :title="'Existing Driver Found'"
      :confirmBtnText="'Go to Driver'"
      :isDelete="false"
      :confirmDisabled="false"
      :isLoading="false"
      @closeDialog="showExistingDriverDialog = false"
      @confirm="goToExistingDriver"
      v-if="showExistingDriverDialog && existingDriverDetails"
    >
      <div class="pa-3">
        <p>
          A driver with that
          {{ isExistingDriverByEmail ? 'email address' : 'mobilenumber' }}
          already exists within <strong>{{ currentCompanyAndDivision }}</strong
          >. The existing driver information is below:
        </p>
        <p class="mt-3 mb-1">
          <strong> Account Information:</strong>
        </p>
        <ul>
          <li>
            Name:
            <strong>{{ existingDriverDetails.name }}</strong>
          </li>

          <li>
            Email:
            <strong>{{ existingDriverDetails.email }}</strong>
          </li>
          <li>
            Mobile:
            <strong>{{
              formatPhoneNumber(existingDriverDetails.mobile)
            }}</strong>
          </li>
        </ul>

        <p class="mt-3 mb-1">
          <strong> Current Associated Fleet Asset Owners:</strong>
        </p>

        <ul>
          <li
            v-for="(
              fleetAssetOwner, index
            ) of getDriversAssociatedFleetAssetOwners(
              existingDriverDetails.driverId,
            )"
            :key="index"
          >
            <strong
              >{{ fleetAssetOwner.name }} {{ fleetAssetOwner.ownerId }}
            </strong>
          </li>
        </ul>

        <p class="mt-3">
          If you would like to associate
          {{ existingDriverDetails.displayName }} with a new fleet asset owner
          you can do so through the associations section from within the drivers
          details. Would you like to view this drivers details?
        </p>
      </div>
    </GDialog>
  </div>
</template>

<script setup lang="ts">
import DriverDetailsUpdateDetails from '@/components/admin/DriverDetails/components/driver_details_update_details.vue';
import AddressSearchAU from '@/components/common/addressing/address-search-au/index.vue';
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import FileUpload from '@/components/common/file-upload/file_upload.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import StatusSelect from '@/components/common/status_select.vue';
import SubcontractorUpdateOperationalStatus from '@/components/common/subcontractor_update_operational_status/index.vue';
import FormFieldRow from '@/components/common/ui-elements/form_field_row.vue';
import RateExpirationSummaryAlert from '@/components/common/ui-elements/rate_expiration_summary_alert.vue';
import { formatPhoneNumber } from '@/helpers/StringHelpers/StringHelpers';
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { PrimaryDriverIdentifier } from '@/interface-models/Company/DivisionCustomConfig/Operations/PrimaryDriverIdentifier';
import { DriverDetails } from '@/interface-models/Driver/DriverDetails/DriverDetails';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import { SubcontractorAssociationUpdateResponse } from '@/interface-models/FleetAssetOwner/SubcontractorAssociation/SubcontractorAssociationUpdateResponse';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { Validation } from '@/interface-models/Generic/Validation';
import { OperationStatus } from '@/interface-models/Generic/WebSocketRequest/OperationStatus';
import { SubcontractorEntityType } from '@/interface-models/InvoiceAdjustment/EntityTypes/SubcontractorEntityType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { sessionManager } from '@/store/session/SessionState';
import { useMittListener } from '@/utils/useMittListener';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  defineProps,
  ref,
} from 'vue';
import { useRouter } from 'vue-router/composables';

const props = withDefaults(
  defineProps<{
    isEdited: boolean;
    driverDetails: DriverDetails;
    canEditUsername: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const router = useRouter();
const driverDetailsStore = useDriverDetailsStore();
const fleetAssetOwnerStore = useFleetAssetOwnerStore();

const validate: Ref<Validation> = ref(validationRules);

const attachmentTypes: Ref<any> = ref(AttachmentTypes);
const showAdminComplianceSettings: Ref<boolean> = ref(false);
const existingDriverByMobile: Ref<DriverDetails | null> = ref(null);
const existingDriverByEmail: Ref<DriverDetails | null> = ref(null);
const showExistingDriverDialog: Ref<boolean> = ref(false);

// boolean flag on whether the user is viewing the existing driver by mobile or
// email. We have this so we can have conditions within the dialog and don't
// require two sepaGdialog dialogs.
const isExistingDriverByEmail: Ref<boolean> = ref(false);
const isLoading: Ref<boolean> = ref(false);
const entityType: Ref<any> = ref(EntityType);

const driverEntityType: SubcontractorEntityType =
  SubcontractorEntityType.DRIVER;

// Allow the operational status dialog to be opened in the following cases:
// - They are NOT retired
// - If they are retired, AND no contact info is required
// - If they are retired AND contact info is required AND contact info is
//   supplied
const allowUpdateOperationalStatus: ComputedRef<boolean> = computed(() => {
  const isRetired = props.driverDetails.isRetired;
  // If driver is inactive, then they must have contact info to be able to
  // enable the input UNLESS they're a generic driver
  const contactInfoRequired = !props.driverDetails.isGenericDriver;
  const hasContactInfo =
    !!props.driverDetails.mobile || !!props.driverDetails.email;
  return !isRetired || !contactInfoRequired || hasContactInfo;
});

/**
 * Returns true of the primaryDriverIdentifier is set to ASSIGNED_ID or
 * ASSIGNED_ID_WITH_NAME. In this case we expect the assignedId field to be used
 * across the app, so the input should be visible and required.
 */
const assignedIdRequired: ComputedRef<boolean> = computed(() => {
  const configuredIdentifier =
    useCompanyDetailsStore().divisionCustomConfig?.operations
      ?.primaryDriverIdentifier;
  return (
    configuredIdentifier === PrimaryDriverIdentifier.ASSIGNED_ID ||
    configuredIdentifier === PrimaryDriverIdentifier.ASSIGNED_ID_WITH_NAME
  );
});

/**
 * Helper function to validate the assigned Fleet CSR ID.
 * Ensures that the entered ID is unique among active fleet assets.
 * @param {string} value - The CSR ID entered by the user.
 * @returns {boolean | string} - Returns `true` if valid, otherwise returns an error message.
 */
function validateDriverAssignedId(value: string): true | string {
  // Fetch the list of active drivers
  const activeDrivers = driverDetailsStore.getDriverList.filter(
    (driver: DriverDetailsSummary) =>
      driver.isActive &&
      (!props.driverDetails._id || driver.id !== props.driverDetails._id),
  );

  // Check if the entered ID already exists among active fleet assets
  const isDuplicate = activeDrivers.some(
    (driver: DriverDetailsSummary) => driver.assignedId === value,
  );

  // Return validation message or true (valid)
  return isDuplicate ? 'This ID is already in use' : true;
}

// The mobile number field is required if the driver is ACTIVE (not retired), or the driver is being
const mobileNumberValidationRequired: ComputedRef<boolean> = computed(() => {
  if (props.driverDetails.isGenericDriver) {
    return false;
  }
  return !props.driverDetails._id || !props.driverDetails.isRetired;
});

// Check if we are authorised to see selects related to compliance
const isAuthorisedAdmin = () => {
  return hasAdminRole();
};

// Controller for switches for licence settings, visible/editable by admin
// only
const driverUsesMobileApp: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.driverDetails.mobileAppRequired;
  },
  set(value: boolean): void {
    if (!isAuthorisedAdmin()) {
      return;
    }
    if (!props.driverDetails.complianceOverrides) {
      props.driverDetails.restoreComplianceDefaults();
    }
    props.driverDetails.complianceOverrides!.usesMobileApp = value;
  },
});

// Controller for switches for licence settings, visible/editable by admin
// only
const licenceComplianceRequired: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.driverDetails.licenceComplianceRequired;
  },
  set(value: boolean): void {
    if (!isAuthorisedAdmin()) {
      return;
    }
    // If complianceOverrides is previously undefined, then set default values
    // to all positive values (as it was previously a standard driver where ALL
    // are required)
    if (!props.driverDetails.complianceOverrides) {
      props.driverDetails.restoreComplianceDefaults();
    }
    props.driverDetails.complianceOverrides!.licenceRequired = value;
  },
});

// Controller for switches for compliance settings, visible/editable by admin
// only
const inductionComplianceRequired: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.driverDetails.inductionComplianceRequired;
  },
  set(value: boolean): void {
    if (!isAuthorisedAdmin()) {
      return;
    }
    // If complianceOverrides is previously undefined, then set default values
    // to all positive values (as it was previously a standard driver where ALL
    // are required)
    if (!props.driverDetails.complianceOverrides) {
      props.driverDetails.complianceOverrides = {
        usesMobileApp: true,
        licenceRequired: true,
        inductionRequired: true,
      };
    }
    props.driverDetails.complianceOverrides.inductionRequired = value;
  },
});

/**
 * Go to route of driver that already exists. Actioned from "Did you mean?" dialog.
 * @returns {void}
 */
function goToExistingDriver(): void {
  const driver =
    isExistingDriverByEmail.value && existingDriverByEmail.value
      ? existingDriverByEmail.value
      : !isExistingDriverByEmail.value && existingDriverByMobile.value
        ? existingDriverByMobile.value
        : null;
  if (driver?.driverId) {
    driverDetailsStore.setSelectedDriverDetailsId(props.driverDetails.driverId);
    router.push({
      name: 'Driver',
      params: {
        name: driver.name.toLowerCase().replace(/ /g, '-'),
        id: driver.driverId,
      },
    });
    driverDetailsStore.setSelectedDriverDetailsId(null);
  }
}

/**
 * Returns the name of the active company name and division Id.
 * @returns {string}
 */
const currentCompanyAndDivision = computed(() => {
  return (
    (useCompanyDetailsStore().companyDetails?.name ?? '') +
    ' - ' +
    sessionManager.getDivisionId()
  );
});

/**
 * Returns the existing driver based on which dialog the user has selected. Mobile / Email
 * @returns {DriverDetails | null} The driver details
 */
const existingDriverDetails: ComputedRef<DriverDetails | null> = computed(
  () => {
    return isExistingDriverByEmail.value
      ? existingDriverByEmail.value
      : existingDriverByMobile.value;
  },
);

/**
 * Sets the existing driver details dialog.
 * @param isOpen whether the dialog is to be opened or not
 * @param isEmail whether the dialog is for the existing email, if false it is for existing mobile.
 * @returns {string}
 */
const setExistingDriverDialog = (isOpen: boolean, isEmail: boolean): void => {
  showExistingDriverDialog.value = isOpen;
  isExistingDriverByEmail.value = isEmail;
};

/**
 * Handles events from the GMobileValidator component when the searched mobile number already exists. Sets local state and shows a "did you mean"? selectable that will open a dialog with the existing drivers information.
 * @param {DriverDetails | null} existingDriver - The existing driver that is emited from the GMobileValidator component. Null if the mobile number does not already exist.
 * @returns {string}
 */
const setExistingDriverMobile = (
  existingDriver: DriverDetails | null,
): void => {
  existingDriverByMobile.value = existingDriver ? existingDriver : null;
};

/**
 * Handles events from the GEmailValidator component when the searched mobile number already exists. Sets local state and shows a "did you mean"? selectable that will open a dialog with the existing drivers information.
 * @param {DriverDetails | null} existingDriver - The existing driver that is emited from the GEmailValidator component. Null if the email address does not already exist.
 * @returns {string}
 */
const setExistingDriverEmail = (existingDriver: DriverDetails | null): void => {
  existingDriverByEmail.value = existingDriver ? existingDriver : null;
};

/**
 * Returns a list of fleet asset owners that are associated to the supplied driverId
 * @param {string} driverId - The driverId that we are looking for matches against within our fleet asset owner list
 * @returns {FleetAssetOwnerSummary[]}
 */
const getDriversAssociatedFleetAssetOwners = (
  driverId: string,
): FleetAssetOwnerSummary[] => {
  return fleetAssetOwnerStore.getOwnerList.filter((x: FleetAssetOwnerSummary) =>
    x.associatedDrivers.includes(driverId),
  );
};

/**
 * Used as key for StatusSelect component to force re-render when statusList is
 * updated
 */
const statusListKey: ComputedRef<string> = computed(() => {
  return `${props.driverDetails.statusList}`;
});

/**
 * Handles response to status update. Used to update the statusList in the
 * driverDetails prop.
 * @param response - contains properties from updated document, including the
 * updated statusList which we'll set to props.driverDetails
 */
function handleSubcontractorEntityUpdate(
  response: SubcontractorAssociationUpdateResponse | null,
): void {
  if (
    response?.entityType === SubcontractorEntityType.DRIVER &&
    response.driverId === props.driverDetails.driverId &&
    response.operationStatus === OperationStatus.SUCCESS &&
    !!response.statusList
  ) {
    props.driverDetails.statusList = response.statusList;
  }
}

useMittListener(
  'updateSubcontractorEntityAssociationResponse',
  handleSubcontractorEntityUpdate,
);
</script>

<style lang="scss" scoped>
.address-label {
  width: 100%;
  height: 48px;
}

.address-label-container {
  padding-top: 40px;
}

.label-container {
  height: 48px;
}

.existing-driver-mobile-select-text {
  position: absolute;
  bottom: 8px;
  right: 0;
  color: var(--highlight);
  font-size: $font-size-12;
  cursor: pointer;
}
</style>
