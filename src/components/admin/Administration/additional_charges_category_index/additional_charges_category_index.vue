<template>
  <div
    id="additional-charges-category-index"
    class="additional-charges-category-index"
  >
    <!-- <v-btn color="error" depressed outline small @click="cancelEdit"
        >Cancel
      </v-btn> -->
    <TableTitleHeader>
      <template #title>
        <GTitle
          title="Additional Charges Category"
          subtitle="Create or update additional charges categories"
          :divider="false"
        />
      </template>

      <template #buttons>
        <GButton
          icon="fal fa-plus"
          :iconRight="false"
          @click="addNewChargeType"
          :disabled="!isAuthorised()"
        >
          Create Charge Category</GButton
        >
      </template>

      <template #inputs>
        <v-flex md6>
          <v-text-field
            appendIcon="search"
            label="Search Category"
            hint="Search Category by Name"
            color="orange"
            solo
            flat
            class="v-solo-custom"
            clearable
            v-model="searchQuery"
          >
          </v-text-field>
        </v-flex>
      </template>
    </TableTitleHeader>
    <AdditionalChargesCategoryTableView
      :additionalChargeTypes="additionalChargeTypes"
      :searchQuery="searchQuery"
      @viewAdditionalChargeRate="editExistingChargeType"
    />
    <AdditionalChargesCategoryEditDialog
      :key="editedAdditionalChargeType?._id || 'new'"
      @cancelEdit="cancelEdit"
      :isDialogOpen.sync="editDialogController"
      :additionalChargeType="editedAdditionalChargeType"
    />
  </div>
</template>

<script setup lang="ts">
import AdditionalChargesCategoryEditDialog from '@/components/admin/Administration/additional_charges_category_index/components/additional_charges_category_edit_dialog.vue';
import AdditionalChargesCategoryTableView from '@/components/admin/Administration/additional_charges_category_index/components/additional_charges_category_table_view.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import { useRootStore } from '@/store/modules/RootStore';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  nextTick,
  ref,
} from 'vue';

const editChargeDialogIsOpen: Ref<boolean> = ref(false);
const editedAdditionalChargeType: Ref<AdditionalChargeType | null> = ref(null);
const searchQuery = ref('');

const additionalChargeTypes: ComputedRef<AdditionalChargeType[]> = computed(
  () => {
    return useRootStore().additionalChargeTypeList;
  },
);

const editDialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return editChargeDialogIsOpen.value;
  },
  set(value: boolean): void {
    if (!value) {
      editedAdditionalChargeType.value = null;
    }
    editChargeDialogIsOpen.value = value;
  },
});

/**
 * Cancels the edit operation.
 */
function cancelEdit() {
  editedAdditionalChargeType.value = null;
}

/**
 * Sets a new additional charge category type.
 */
function addNewChargeType(): void {
  openChargeItemInDialog(new AdditionalChargeType());
}

/**
 * For the provided id, finds the associated AdditionalChargeCategoryItem and sets it to
 * the edit variable, which will open the edit dialog.
 * @param {string} id - The ID of the additional charge type.
 */
function editExistingChargeType(id: string): void {
  const additionalCharge: AdditionalChargeType | undefined =
    additionalChargeTypes.value.find(
      (category: AdditionalChargeType) => category._id === id,
    );
  if (!additionalCharge) {
    return;
  }
  openChargeItemInDialog(additionalCharge);
}

/**
 * For the provided id, finds the associated AdditionalChargeCategoryItem and sets it to
 * the edit variable, which will open the edit dialog.
 * @param {string} id - The ID of the additional charge type.
 */
function openChargeItemInDialog(item: AdditionalChargeType): void {
  editedAdditionalChargeType.value = item;
  nextTick(() => {
    editDialogController.value = true;
  });
}

/**
 * Checks if the user is authorized.
 * @returns {boolean} True if the user has admin or head office role, false otherwise.
 */
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}
</script>

<style scoped lang="scss"></style>
