<template>
  <div class="hire-contract-container" v-if="availableDateRanges !== null">
    <div v-if="contract" :class="isAssetInformation ? 'pa-3' : ''">
      <v-layout :mb-3="!contractIsActive">
        <v-flex md12>
          <v-alert
            type="warning"
            :value="!contractIsActive"
            :class="isAssetInformation ? 'my-0' : ''"
          >
            <span class="pl-3"
              >Charges will not be generated as this contract is not currently
              active.</span
            >
          </v-alert>
        </v-flex>
      </v-layout>
      <v-layout wrap>
        <v-flex md12 pb-3
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">1. Contract Information</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Document Number
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-text-field
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                :rules="[validate.required]"
                v-model="contract.documentNumber"
                label="Document Number"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Contract Number
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-text-field
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :rules="[validate.required]"
                :disabled="!isEditedController"
                v-model="contract.contractNumber"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Hire Rate Per Day ($)
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-text-field
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                type="number"
                v-model.number="contract.hireRate"
                :rules="[validate.required]"
                label="Hire Rate"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6 class="subheader--faded pr-3 pb-0">Odometer Start</h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-text-field
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                type="number"
                v-model.number="contract.odometer.start"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6 class="subheader--faded pr-3 pb-0">Odometer Finish</h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-text-field
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                type="number"
                v-model.number="contract.odometer.finish"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded form-field-required-marker pr-3 pb-0"
                >
                  Contract Valid From
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <DateTimeInputs
                :epochTime.sync="contract.validFromDate"
                :enableValidation="true"
                :type="DateTimeType.DATE_START_OF_DAY"
                dateLabel="Contract Valid From"
                :readOnly="!isEditedController"
                :soloInput="true"
                :boxInput="false"
                :isRequired="true"
                :hintTextType="HintTextType.FORMATTED_SELECTION"
                :allowedDates="availableDateRanges.selectableStartDates"
                @dateTimeUpdated="startDateUpdated"
              ></DateTimeInputs>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded form-field-required-marker pr-3 pb-0"
                >
                  Contract Valid To
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <DateTimeInputs
                :epochTime.sync="contract.validToDate"
                :enableValidation="true"
                :type="DateTimeType.DATE_END_OF_DAY"
                dateLabel="Contract Valid To"
                :readOnly="!isEditedController"
                :soloInput="true"
                :boxInput="false"
                :isRequired="true"
                :hintTextType="HintTextType.FORMATTED_SELECTION"
                :allowedDates="availableDateRanges.selectableEndDates"
                @dateTimeUpdated="endDateUpdated"
              ></DateTimeInputs>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6 class="subheader--faded pr-3 pb-0">Hire Contract Upload</h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <file-upload
                :imageLabel="'Hire Contract'"
                :isPDF="true"
                :attachmentSingle="false"
                :documentTypeId="attachmentTypes.EQUIPMENT_HIRE_CONTRACT"
                :attachmentArray="contract.contractDocuments"
                :formDisabled="!isEditedController"
              >
              </file-upload>
            </v-flex>
          </v-layout>
        </v-flex>

        <v-flex md12 py-3
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3">
              2. Contract Distance Configuration
            </h5>
            <!-- Configuration for distance requirements defined in the contract. -->
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Allowed Travel Distance (KM)
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-text-field
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                type="number"
                :rules="[validate.required]"
                v-model.number="contract.travelDistance.distance"
                label="Distance (Km)"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Per
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md4 pr-2>
              <v-text-field
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                :rules="[validate.required]"
                v-model.number="contract.travelDistance.distanceIncrement"
                label="Distance Increment"
              />
            </v-flex>
            <v-flex md4>
              <v-select
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :items="rateMultipliers"
                item-text="longName"
                item-value="id"
                :rules="[validate.required]"
                :disabled="!isEditedController"
                v-model="contract.travelDistance.distanceMultiplier"
              />
            </v-flex>
          </v-layout>
        </v-flex>

        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Excess Charge Per km ($)
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-text-field
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                type="number"
                :disabled="!isEditedController"
                v-model.number="contract.travelDistance.excessRate"
                label="Excess Charge Per km ($)"
                :rules="[validate.required]"
                hint="The excess charge accrued per KM over the allowable distance"
              />
            </v-flex>
          </v-layout>
        </v-flex>

        <v-flex md12 py-3
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3">3. Contract Insurance</h5>
            <!-- Insurance Policy defined within the contract. -->
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Damage Waiver Rate
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-text-field
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                type="number"
                :rules="[validate.required]"
                min="0"
                v-model.number="contract.insurance.waiverRate"
                label="Damage Waiver Rate"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Waiver Rate Type
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-select
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                :items="numberValueTypes"
                v-model="contract.insurance.waiverTypeId"
                item-text="longName"
                item-value="id"
                :rules="[validate.required]"
                label="Waiver Rate Type"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Excess Fee
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-text-field
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                type="number"
                :rules="[validate.required]"
                min="0"
                v-model.number="contract.insurance.excessRate"
                label="Excess Increment"
              />
            </v-flex>
          </v-layout>
        </v-flex>

        <v-flex md12 py-3
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3">4. Administration Fee</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Rate
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-text-field
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                type="number"
                min="0"
                :rules="[validate.required]"
                v-model.number="contract.administrationFee.rate"
                label="Administration Fee Rate"
              />
            </v-flex>
          </v-layout>
        </v-flex>

        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6
                  class="subheader--faded pr-3 pb-0 form-field-required-marker"
                >
                  Rate Type
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-select
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                :items="numberValueTypes"
                v-model="contract.administrationFee.chargeTypeId"
                item-text="longName"
                :rules="[validate.required]"
                item-value="id"
                label="Waiver Rate Type"
              />
            </v-flex>
          </v-layout>
        </v-flex>

        <v-flex md12 py-3
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3">5. Job Rate</h5>
            <!-- This rate will be used to provide an estimated margin on completed jobs
        by this asset. -->
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6 class="subheader--faded pr-3 pb-0">Job Rate ($)</h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-text-field
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                type="number"
                label="Job Rate"
                v-model.number="contract.jobRate"
                hint="This rate will be used to provide an estimated margin in operations review screen"
              />
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6 class="subheader--faded pr-3 pb-0">Job Rate Multiplier</h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-select
                :class="!isEditedController ? 'solo-input-disable-display' : ''"
                solo
                flat
                class="v-solo-custom"
                :disabled="!isEditedController"
                :items="rateMultipliers"
                item-text="longName"
                item-value="id"
                v-model="contract.jobRateMultiplier"
                label="Job Rate Multiplier"
              />
            </v-flex>
          </v-layout>
        </v-flex>

        <v-flex md12 py-3
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3">6. Contract Active Status</h5>

            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout>
            <v-flex md4>
              <v-layout align-center class="label-container">
                <h6 class="subheader--faded form-field-required pr-3 pb-0">
                  Is this contract active?
                </h6>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <StatusSelect
                key="retirementStatus"
                :showSecondaryStatusList="false"
                :statusCategory="11"
                :soloInput="true"
                :statusList="contract.statusList"
                :resetSelectedSecondaryStatus="false"
                :formDisabled="!isEditedController"
              >
              </StatusSelect>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </div>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import FileUpload from '@/components/common/file-upload/file_upload.vue';
import StatusSelect from '@/components/common/status_select.vue';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import HireContract from '@/interface-models/FleetAsset/EquipmentHire/HireContract';
import { HireContractSummary } from '@/interface-models/FleetAsset/EquipmentHire/HireContractSummary';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import numberValueTypes from '@/interface-models/Generic/NumberValueTypes/NumberValueTypes';
import { rateMultipliers } from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import { Validation } from '@/interface-models/Generic/Validation';
import {
  AvailableDateRange,
  DateRange,
} from '@/interface-models/ServiceRates/AvailableDateRange';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { Ref, WritableComputedRef, computed, onMounted, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    fleetAssetId: string;
    contract: HireContract;
    isAssetInformation?: boolean;
    isEdited?: boolean;
    contractIsActive?: boolean;
  }>(),
  {
    isAssetInformation: false,
    isEdited: false,
    contractIsActive: false,
  },
);

// Validation rules
const validate: Validation = validationRules;

// Attachment types
const attachmentTypes = AttachmentTypes;
const editView = ref<boolean>(false);
const availableDateRanges: Ref<AvailableDateRange | null> = ref(null);

function startDateUpdated(value: number) {
  if (!availableDateRanges.value) {
    return;
  }
  availableDateRanges.value.startDate = value;
  availableDateRanges.value.setSelectableEndDates();
}

function endDateUpdated(value: number) {
  if (!availableDateRanges.value) {
    return;
  }
  availableDateRanges.value.endDate = value;
  availableDateRanges.value.setSelectableStartDates();
}

const isEditedController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    if (props.isAssetInformation || props.isEdited === undefined) {
      return editView.value;
    } else {
      return props.isEdited;
    }
  },
  set(value: boolean): void {
    if (props.isAssetInformation || props.isEdited === undefined) {
      editView.value = value;
    }
  },
});

const contractIsActive = computed(() => {
  if (!props.contract) {
    return false;
  }
  return props.contract.statusList.includes(4);
});

function setAvailableDateRange() {
  if (!props.contract) {
    return null;
  }
  const dateRanges: DateRange[] =
    useFleetAssetStore().hireContractSummaryList.map(
      (x: HireContractSummary) => ({
        id: x._id,
        startDate: x.validFromDate,
        endDate: x.validToDate,
      }),
    );

  availableDateRanges.value = new AvailableDateRange(
    dateRanges,
    props.contract._id,
    props.contract.validFromDate,
    props.contract.validToDate,
    [],
    [],
  );
  availableDateRanges.value.setSelectableStartDates();
  availableDateRanges.value.setSelectableEndDates();
}

// Lifecycle hook
onMounted(() => {
  setAvailableDateRange();
  if (props.contract && !props.contract._id) {
    isEditedController.value = true;
    if (availableDateRanges.value) {
      props.contract.validFromDate =
        availableDateRanges.value.newContractStartDate;
    }
  }
  setAvailableDateRange();
});
</script>

<style lang="scss" scoped>
.hire-contract-container {
  .label-container {
    height: 48px;
  }
}
</style>
