<template>
  <div id="additional-charges-index" class="additional-charges-index">
    <!-- <v-btn color="error" depressed outline small @click="cancelEdit"
        >Cancel
      </v-btn> -->
    <TableTitleHeader>
      <!-- Title slot -->
      <template #title>
        <GTitle
          title="Additional Charges Administration"
          subtitle="Add or update additional charges"
          :divider="false"
        />
      </template>

      <template #buttons>
        <GButton
          icon="fal fa-plus"
          :iconRight="false"
          @click="addNewChargeItem"
          :disabled="!isAuthorised()"
        >
          Add New Charge</GButton
        >
      </template>
    </TableTitleHeader>
    <v-layout class="pt-3 px-3 pb-0" v-if="showFormErrors">
      <v-flex md12>
        <v-alert :value="true" outline
          >A Charge Category must be selected. If a appropriate category does
          not exist you can create one from the previous menu.</v-alert
        >
      </v-flex>
    </v-layout>

    <v-flex md12 class="pb-2">
      <AdditionalChargesTableView
        :additionalChargeItems="additionalChargeRates"
        :additionalChargeTypes="additionalChargeTypes"
        @viewAdditionalChargeRate="editExistingChargeItem"
      />
    </v-flex>
    <AdditionalChargesEditDialog
      :key="editedAdditionalChargeItem?._id || 'new'"
      @cancelEdit="cancelEdit"
      :isDialogOpen.sync="editDialogController"
      :additionalChargeTypes="additionalChargeTypes"
      :additionalChargeItem="editedAdditionalChargeItem"
      :isTollAdminAndHandlingCharge="isEditingTollAdminAndHandlingCharge"
    />
  </div>
</template>

<script setup lang="ts">
import AdditionalChargesEditDialog from '@/components/admin/Administration/additional_charges_index/components/additional_charges_edit_dialog.vue';
import AdditionalChargesTableView from '@/components/admin/Administration/additional_charges_index/components/additional_charges_table_view.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import { useRootStore } from '@/store/modules/RootStore';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  nextTick,
  ref,
} from 'vue';

// const additionalChargeItem: Ref<AdditionalChargeItem | null> = ref(null);
// const additionalChargeType: Ref<AdditionalChargeType | null> = ref(null);
const showFormErrors: Ref<boolean> = ref(false);
// const isEdited: Ref<boolean> = ref(false);

const editChargeDialogIsOpen: Ref<boolean> = ref(false);
const editedAdditionalChargeItem: Ref<AdditionalChargeItem | null> = ref(null);
const additionalChargeItems: ComputedRef<AdditionalChargeItem[]> = computed(
  () => {
    return useRootStore().additionalChargeItemList;
  },
);

const additionalChargeTypes: ComputedRef<AdditionalChargeType[]> = computed(
  () => {
    return useRootStore().additionalChargeTypeList.filter(
      (type) => type.shortName !== 'FRA' && type.shortName !== 'OVA',
    );
  },
);

const editDialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return editChargeDialogIsOpen.value;
  },
  set(value: boolean): void {
    if (!value) {
      editedAdditionalChargeItem.value = null;
    }
    editChargeDialogIsOpen.value = value;
  },
});

const isEditingTollAdminAndHandlingCharge: ComputedRef<boolean> = computed(
  () => {
    return (
      !!useRootStore().tollAdminAndHandlingId &&
      editedAdditionalChargeItem.value?._id ===
        useRootStore().tollAdminAndHandlingId
    );
  },
);
/**
 * Computes the additional charge rates.
 * @returns {AdditionalChargeItem[]} The organized additional charge rates.
 */
const additionalChargeRates: ComputedRef<AdditionalChargeItem[]> = computed(
  () => {
    let organisedRates: AdditionalChargeItem[] = [];
    for (const type of additionalChargeTypes.value) {
      const itemFilteredByType = additionalChargeItems.value.filter(
        (rate) => rate.typeReferenceId === type._id,
      );
      organisedRates = organisedRates.concat(itemFilteredByType);
    }
    return organisedRates;
  },
);

/**
 * Cancels the edit operation.
 */
function cancelEdit() {
  editedAdditionalChargeItem.value = null;
  showFormErrors.value = false;
}

/**
 * Sets a new additional charge item.
 */
function addNewChargeItem(): void {
  openChargeItemInDialog(new AdditionalChargeItem());
}

/**
 * For the provided id, finds the associated AdditionalChargeItem and sets it to
 * the edit variable, which will open the edit dialog.
 * @param {string} id - The ID of the additional charge rate.
 */
function editExistingChargeItem(id: string): void {
  const additionalCharge: AdditionalChargeItem | undefined =
    additionalChargeRates.value.find(
      (rate: AdditionalChargeItem) => rate._id === id,
    );
  if (!additionalCharge) {
    return;
  }
  openChargeItemInDialog(additionalCharge);
}

// /**
//  * Sets a new additional charge type.
//  */
// function setNewAdditionalChargeType(): void {
//   additionalChargeType.value = new AdditionalChargeType();
// }

/**
 * For the provided id, finds the associated AdditionalChargeItem and sets it to
 * the edit variable, which will open the edit dialog.
 * @param {string} id - The ID of the additional charge rate.
 */
function openChargeItemInDialog(item: AdditionalChargeItem): void {
  editedAdditionalChargeItem.value = item;
  nextTick(() => {
    editDialogController.value = true;
  });
}

// /**
//  * Validates an additional charge item.
//  * This function checks if the typeReferenceId of the additional charge item is not an empty string.
//  * If it is, it sets showFormErrors to true and returns false.
//  * @param {AdditionalChargeItem} item - The additional charge item to validate.
//  * @returns {boolean} True if the additional charge item is valid, false otherwise.
//  */
// function isValidAdditionalChargeItem(item: AdditionalChargeItem): boolean {
//   if (item.typeReferenceId === '') {
//     showFormErrors.value = true;
//     return false;
//   }
//   showFormErrors.value = false;
//   return true;
// }

/**
 * Checks if the user is authorized.
 * @returns {boolean} True if the user has admin or head office role, false otherwise.
 */
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}
</script>

<style scoped lang="scss"></style>
