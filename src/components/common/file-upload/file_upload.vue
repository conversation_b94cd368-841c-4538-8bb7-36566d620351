<template>
  <div class="professional-file-upload" :class="{ 'upload-disabled': isUploadDisabled }">
    <!-- Header Section -->
    <div class="upload-header" v-if="imageLabel">
      <h3 class="upload-title">{{ imageLabel }}</h3>
      <div class="upload-info">
        <v-chip
          :color="attachmentSingle ? 'blue' : 'green'"
          small
          outlined
          class="mode-chip"
        >
          <v-icon left size="16">
            {{ attachmentSingle ? 'fas fa-file-outline' : 'fas fa-file-multiple-outline' }}
          </v-icon>
          {{ attachmentSingle ? 'Single File' : 'Multiple Files' }}
        </v-chip>
        <span class="file-count" v-if="!attachmentSingle && attachmentList.length > 0">
          {{ attachmentList.length }} file{{ attachmentList.length !== 1 ? 's' : '' }} uploaded
        </span>
      </div>
    </div>

    <!-- Progress Bar -->
    <v-progress-linear
      :active="showLoadingIndicator || awaitingImageDownload || isUploading"
      :indeterminate="true"
      color="primary"
      height="3"
      class="upload-progress"
      rounded
    ></v-progress-linear>

    <!-- Drop Zone -->
    <div
      class="drop-zone"
      :class="{
        'drop-zone-active': isDragOver,
        'drop-zone-disabled': isUploadDisabled,
        'has-files': attachmentList.length > 0
      }"
      @click="pickFile"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
      v-if="attachmentList.length === 0 || !attachmentSingle"
    >
      <div class="drop-zone-content">
        <div class="upload-icon-container">
          <v-icon
            :size="isUploading ? 40 : 48"
            :color="isUploadDisabled ? 'grey' : 'primary'"
            class="upload-main-icon"
            :class="{ 'rotating': isUploading }"
          >
            {{ isUploading ? 'fas fa-cloud-upload' : isDragOver ? 'fas fa-cloud-download' : 'fas fa-cloud-upload-outline' }}
          </v-icon>
        </div>
        <v-icon>fas fa-paperclip</v-icon>
        <div class="upload-text">
          <h4 class="upload-primary-text">
            {{
              isUploading ? 'Uploading files...' :
              isDragOver ? 'Drop files here' :
              'Drop files here or click to browse'
            }}
          </h4>
          <p class="upload-secondary-text" v-if="!isUploading">
            {{ getAcceptedTypesText() }}
          </p>
          <p class="upload-hint" v-if="!isUploading && !isDragOver">
            Maximum file size: 10MB
          </p>
        </div>
      </div>
    </div>

    <!-- Unified File Gallery Card -->
    <div class="unified-file-gallery" v-if="attachmentList.length > 0">
      <div class="gallery-header" v-if="!attachmentSingle">
        <h4 class="gallery-title">
          <v-icon left color="primary">fas fa-folder-multiple-image</v-icon>
          Uploaded Files ({{ attachmentList.length }})
        </h4>
      </div>

      <div class="gallery-grid">
        <div
          class="gallery-item"
          v-for="(attachmentItem, index) in attachmentList"
          :key="attachmentItem.id || index"
          @click="openGalleryViewer(index)"
        >
          <div class="gallery-item-overlay"></div>

          <!-- File Preview -->
          <div class="file-preview">
            <div class="file-preview-content">
              <!-- High-Quality Image Preview -->
              <div
                v-if="isImageFile(attachmentItem.mimeType) && (imageSrc || attachmentItem.data)"
                class="high-quality-image-container"
              >
                <img
                  :src="imageSrc || attachmentItem.data"
                  :alt="getDisplayFileName(attachmentItem.name)"
                  class="high-quality-image"
                  @load="onImageLoad"
                  @error="onImageError"
                />
                <div v-if="imageLoading" class="image-loading-overlay">
                  <v-progress-circular indeterminate color="primary" size="32"></v-progress-circular>
                </div>
              </div>

              <!-- Enhanced File Icon for Non-Images -->
              <div v-else class="enhanced-file-icon-container">
                <div class="icon-background" :class="getFileIconClass(attachmentItem.mimeType)">
                  <v-icon
                    :size="56"
                    color="white"
                    class="enhanced-file-icon"
                  >
                    {{ getFileIcon(attachmentItem.mimeType) }}
                  </v-icon>
                </div>
                <div class="file-type-badge">
                  {{ getFileTypeLabel(attachmentItem.mimeType) }}
                </div>
              </div>
            </div>

            <!-- File Overlay -->
            <div class="file-overlay">
              <v-btn
                icon
                small
                color="white"
                class="overlay-btn"
                @click.stop="viewFile(attachmentItem)"
              >
                <v-icon color="black">fas fa-eye</v-icon>
              </v-btn>
            </div>
          </div>

          <!-- File Info -->
          <div class="file-info">
            <div class="file-name" :title="getDisplayFileName(attachmentItem.name)">
              {{ getDisplayFileName(attachmentItem.name) }}
            </div>
            <div class="file-details">
              <span class="file-type">{{ getFileTypeLabel(attachmentItem.mimeType) }}</span>
              <span class="file-size" v-if="attachmentItem.size">{{ formatFileSize(attachmentItem.size) }}</span>
            </div>
          </div>

          <!-- File Actions -->
          <div class="file-actions">
            <v-btn
              icon
              small
              color="primary"
              @click.stop="viewFile(attachmentItem)"
              :disabled="awaitingImageDownload"
              title="View"
            >
              <v-icon size="18">fas fa-eye</v-icon>
            </v-btn>

            <v-btn
              icon
              small
              color="success"
              @click.stop="downloadFile(attachmentItem)"
              :disabled="awaitingImageDownload"
              title="Download"
            >
              <v-icon size="18">fas fa-download</v-icon>
            </v-btn>

            <v-btn
              v-if="!isAccountRecovery"
              icon
              small
              color="error"
              @click.stop="confirmDelete(attachmentItem)"
              :disabled="isUploadDisabled"
              title="Delete"
            >
              <v-icon size="18">fas fa-trash-alt</v-icon>
            </v-btn>

            <v-btn
              v-if="isAccountRecovery"
              icon
              small
              color="error"
              @click.stop="emit('setAttachment', null)"
              :disabled="formDisabled"
              title="Remove"
            >
              <v-icon size="18">fas fa-times</v-icon>
            </v-btn>
          </div>
        </div>
      </div>
    </div>

    <!-- Hidden File Input -->
    <input
      type="file"
      ref="fileInput"
      :id="'fileInput' + documentTypeId"
      style="display: none"
      :disabled="isUploadDisabled"
      :accept="acceptedFileTypes"
      :multiple="!attachmentSingle"
      @change="handleFileSelect"
    />

    <!-- Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400">
      <v-card>
        <v-card-title class="headline">
          <v-icon color="error" class="mr-2">fas fa-exclamation-circle</v-icon>
          Confirm Deletion
        </v-card-title>
        <v-card-text>
          Are you sure you want to remove "{{ fileToDelete?.name }}"? This action cannot be undone.
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn color="error" @click="deleteFile">Delete</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Advanced Gallery Viewer -->
    <!-- <v-dialog
      v-model="showGalleryViewer"
      fullscreen
      hide-overlay
      transition="dialog-bottom-transition"
      @keydown.esc="closeGalleryViewer"
      @keydown.left="previousImage"
      @keydown.right="nextImage"
    >
      <v-card class="gallery-viewer-card">
        <v-app-bar dark color="black" class="gallery-header-bar">
          <v-btn icon @click="closeGalleryViewer">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
          <v-toolbar-title>
            {{ getDisplayFileName(currentGalleryFile?.name || '') }}
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <div class="gallery-counter">
            {{ currentGalleryIndex + 1 }} of {{ attachmentList.length }}
          </div>
          <v-btn icon @click="downloadFile(currentGalleryFile)" class="ml-2">
            <v-icon>fas fa-download</v-icon>
          </v-btn>
        </v-app-bar>

        <div class="gallery-thumbnails" v-if="attachmentList.length > 1">
          <div class="thumbnail-container">
            <div
              v-for="(item, index) in attachmentList"
              :key="item.id || index"
              class="gallery-thumbnail"
              :class="{ 'active': index === currentGalleryIndex }"
              @click="setCurrentGalleryIndex(index)"
            >
              <div v-if="isImageFile(item.mimeType)" class="thumbnail-image-wrapper">
                <img :src="item.data" :alt="getDisplayFileName(item.name)" class="thumbnail-image" />
              </div>
              <div v-else class="thumbnail-icon-wrapper">
                <v-icon color="white" size="24">{{ getFileIcon(item.mimeType) }}</v-icon>
              </div>
              <div class="thumbnail-overlay">
                <span class="thumbnail-index">{{ index + 1 }}</span>
              </div>
            </div>
          </div>
        </div>
      </v-card>
    </v-dialog> -->

    <!-- Simple File Viewer Dialog (for single file view) -->
    <!-- <v-dialog v-model="showViewerDialog" max-width="800">
      <v-card class="dark-theme-card">
        <v-card-title class="d-flex align-center dark-theme-header">
          <v-icon class="mr-2" color="primary">fas fa-file</v-icon>
          {{ getDisplayFileName(viewingFile?.name || '') }}
          <v-spacer></v-spacer>
          <v-btn icon @click="showViewerDialog = false" color="white">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text class="pa-0 dark-theme-content">
          <div class="simple-file-viewer">
            <div v-if="viewingFile && isImageFile(viewingFile.mimeType)" class="high-quality-image-container">
              <img
                :src="viewingFile.data"
                :alt="getDisplayFileName(viewingFile.name)"
                class="high-quality-image"
                @load="onImageLoad"
                @error="onImageError"
              />
            </div>
            <div v-else class="simple-non-image-viewer">
              <div class="icon-background" :class="getFileIconClass(viewingFile?.mimeType || '')">
                <v-icon size="80" color="white">
                  {{ getFileIcon(viewingFile?.mimeType || '') }}
                </v-icon>
              </div>
              <h4 class="mt-4 white--text">{{ getFileTypeLabel(viewingFile?.mimeType || '') }}</h4>
              <p class="text-caption grey--text">Preview not available for this file type</p>
            </div>
          </div>
        </v-card-text>
        <v-card-actions class="dark-theme-actions">
          <v-btn color="primary" @click="downloadFile(viewingFile)">
            <v-icon left>fas fa-download</v-icon>
            Download
          </v-btn>
          <v-spacer></v-spacer>
          <v-btn text color="white" @click="showViewerDialog = false">Close</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog> -->
  </div>
</template>

<script setup lang="ts">
import {
  downloadAttachment,
  validMimeTypes
} from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { Attachment } from '@/interface-models/Generic/Attachment/Attachment';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { computed, ComputedRef, onMounted, Ref, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    documentTypeId: number;
    attachmentArray?: Attachment[];
    attachment?: Attachment | null;
    attachmentSingle: boolean;
    imageLabel: string;
    formDisabled?: boolean;
    isPDF?: boolean;
    hideIcon?: boolean;
    isLoading?: boolean;
    // isAccountRecovery will emit the attachment back to parent instead of sending a save request.
    isAccountRecovery?: boolean;
  }>(),
  {
    attachment: null,
    isPDF: false,
    hideIcon: false,
    formDisabled: false,
    isLoading: false,
    isAccountRecovery: false,
    attachmentArray: () => [],
  },
);

const awaitingImageSaveFileName: Ref<string | null> = ref(null);
const awaitingImageDownload: Ref<boolean> = ref(false);
const imageSrc: Ref<string | null> = ref(null);
const fileInput = ref<HTMLInputElement | null>(null);
const isUploading: Ref<boolean> = ref(false);
const isDragOver: Ref<boolean> = ref(false);
const showDeleteDialog: Ref<boolean> = ref(false);
const showViewerDialog: Ref<boolean> = ref(false);
const showGalleryViewer: Ref<boolean> = ref(false);
const fileToDelete: Ref<Attachment | null> = ref(null);
const viewingFile: Ref<Attachment | null> = ref(null);
const currentGalleryIndex: Ref<number> = ref(0);
const imageLoading: Ref<boolean> = ref(false);
const galleryControlsVisible: Ref<boolean> = ref(true);

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'resetAttachmentData'): void;
  (event: 'setAttachment', attachment: Attachment | null): void;
}>();

const showLoadingIndicator: ComputedRef<boolean> = computed(() => {
  return (
    props.isLoading &&
    awaitingImageSaveFileName.value !== null &&
    awaitingImageSaveFileName.value !== ''
  );
});

const isUploadDisabled: ComputedRef<boolean> = computed(() => {
  return props.formDisabled || isUploading.value || showLoadingIndicator.value;
});

const acceptedFileTypes: ComputedRef<string> = computed(() => {
  return validMimeTypes.join(',');
});

const attachmentList: ComputedRef<Attachment[]> = computed(() => {
  if (props.attachmentSingle) {
    if (props.attachment && (props.attachment.id || props.isAccountRecovery)) {
      return [props.attachment];
    } else {
      return [];
    }
  } else {
    return props.attachmentArray.filter(
      (x: Attachment) => x.documentTypeId === props.documentTypeId,
    );
  }
});

const imageFiles: ComputedRef<Attachment[]> = computed(() => {
  return attachmentList.value.filter(file => isImageFile(file.mimeType));
});

const currentGalleryFile: ComputedRef<Attachment | null> = computed(() => {
  return attachmentList.value[currentGalleryIndex.value] || null;
});

// Helper function to get display filename (remove UUID prefix)
function getDisplayFileName(filename: string): string {
  if (!filename) {return 'Untitled';}
  // Remove UUID prefix (36 chars + hyphen) if present
  const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}-/i;
  return filename.replace(uuidPattern, '');
}

// Helper function to get file type label
function getFileTypeLabel(mimeType: string): string {
  const typeMap: Record<string, string> = {
    'application/pdf': 'PDF',
    'text/csv': 'CSV',
    'text/plain': 'Text',
    'application/json': 'JSON',
    'application/xml': 'XML',
    'text/xml': 'XML',
    'application/msword': 'Word',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel'
  };

  if (typeMap[mimeType]) {return typeMap[mimeType];}
  if (mimeType.startsWith('image/')) {return 'Image';}
  return 'File';
}

// Helper function to get file icon
function getFileIcon(mimeType: string): string {
  if (mimeType === 'application/pdf') {return 'mdi-file-pdf-box';}
  if (mimeType.startsWith('image/')) {return 'mdi-file-image';}
  if (mimeType === 'text/csv' || mimeType.includes('spreadsheet')) {return 'mdi-file-excel';}
  if (mimeType.includes('wordprocessing') || mimeType === 'application/msword') {return 'mdi-file-word';}
  if (mimeType === 'text/plain') {return 'mdi-file-document';}
  if (mimeType === 'application/json' || mimeType.includes('xml')) {return 'mdi-file-code';}
  return 'mdi-file';
}

// Helper function to get file icon class for background styling
function getFileIconClass(mimeType: string): string {
  if (mimeType === 'application/pdf') {return 'pdf-icon';}
  if (mimeType.startsWith('image/')) {return 'image-icon';}
  if (mimeType === 'text/csv' || mimeType.includes('spreadsheet')) {return 'excel-icon';}
  if (mimeType.includes('wordprocessing') || mimeType === 'application/msword') {return 'word-icon';}
  if (mimeType === 'text/plain') {return 'text-icon';}
  if (mimeType === 'application/json' || mimeType.includes('xml')) {return 'code-icon';}
  return 'default-icon';
}

// Helper function to check if file is an image
function isImageFile(mimeType: string): boolean {
  return mimeType.startsWith('image/') && !mimeType.includes('heic') && !mimeType.includes('heif');
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) {return '0 Bytes';}
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Helper function to get accepted file types text
function getAcceptedTypesText(): string {
  const types = ['Images', 'PDFs', 'Documents', 'Spreadsheets'];
  return `Supported: ${types.join(', ')}`;
}

async function getFull(documentId: string, download: boolean) {
  // Early return if the function is called during account recovery or without
  // a document ID or if the user is already waiting for the download response
  if (props.isAccountRecovery || !documentId || awaitingImageDownload.value) {
    if (!documentId) {
      showNotification(GENERIC_ERROR_MESSAGE, { title: 'File Upload' });
    }
    return;
  }

  awaitingImageDownload.value = true;
  try {
    const fullAttachment =
      await useAttachmentStore().getAttachmentById(documentId);

    // If the attachment is found, attempt to download it
    if (fullAttachment) {
      if (download) {
        const downloadSuccess = downloadAttachment(fullAttachment);
        // If the download fails, show an error notification to the user
        if (!downloadSuccess) {
          showNotification(GENERIC_ERROR_MESSAGE, { title: 'File Download' });
        }
      }
      return fullAttachment.data;
    } else if (!fullAttachment) {
      showNotification(
        'Something went wrong. The attachment could not be found.',
        { title: 'File Upload' },
      );
    }
  } finally {
    awaitingImageDownload.value = false;
  }
}

// Helper function to get the full image URL
async function loadImageSrc(attachment: Attachment) {
  try {
    if (
      attachment &&
      attachment.mimeType &&
      attachment.mimeType.includes('image') &&
      !attachment.mimeType.includes('heic') &&
      !attachment.mimeType.includes('heif')
    ) {
      const imageUrl = await displayAttachmentImage(attachment.id);
      imageSrc.value = imageUrl ? imageUrl : null;
    } else {
      imageSrc.value = null;
    }
  } catch (error) {
    console.error('Error loading image source: ', error);
    imageSrc.value = null;
  }
}

async function displayAttachmentImage(documentId: string) {
  try {
    const fullAttachment = await getFull(documentId, false);
    if (fullAttachment) {
      return fullAttachment;
    }
    return null;
  } catch (error) {
    console.error('Error fetching attachment: ', error);
    return null;
  }
}

function deleteAttachment(documentId: string): void {
  if (!fileInput.value) {
    (fileInput as any).value = '';
  }

  if (!props.attachmentSingle) {
    const index = props.attachmentArray.findIndex(
      (item: any) => item.id === documentId,
    );
    props.attachmentArray.splice(index, 1);
  }
  if (props.attachmentSingle && props.attachment) {
    props.attachment.id = '';
    props.attachment.documentTypeId = props.documentTypeId;
    props.attachment.mimeType = '';
    props.attachment.data = '';
    props.attachment.name = '';
    props.attachment.timestamp = moment().valueOf();
    props.attachment.approvalStatus = [];
  }
}

// Drag and drop handlers
function handleDragOver(event: DragEvent) {
  if (isUploadDisabled.value) {return;}
  event.preventDefault();
  isDragOver.value = true;
}

function handleDragLeave(event: DragEvent) {
  event.preventDefault();
  isDragOver.value = false;
}

function handleDrop(event: DragEvent) {
  event.preventDefault();
  isDragOver.value = false;

  if (isUploadDisabled.value) {return;}

  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    processFiles(files);
  }
}

// File selection handlers
function pickFile() {
  if (isUploadDisabled.value) {return;}
  const fileInputElement = fileInput.value;
  if (fileInputElement) {
    fileInputElement.click();
  }
}

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  if (files && files.length > 0) {
    processFiles(files);
  }
}

// File processing
function processFiles(files: FileList) {
  const fileArray = Array.from(files);

  // Validate file count for single file mode
  if (props.attachmentSingle && fileArray.length > 1) {
    showNotification('Only one file is allowed in single file mode.', {
      type: HealthLevel.ERROR,
    });
    return;
  }

  // Process each file
  fileArray.forEach(file => {
    if (validateFile(file)) {
      uploadFile(file);
    }
  });
}

function validateFile(file: File): boolean {
  // Check file type
  if (!validMimeTypes.includes(file.type)) {
    showNotification(`File type "${file.type}" is not supported.`, {
      type: HealthLevel.ERROR,
    });
    return false;
  }

  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    showNotification(`File "${file.name}" is too large. Maximum size is 10MB.`, {
      type: HealthLevel.ERROR,
    });
    return false;
  }

  return true;
}

// Image loading handlers
function onImageLoad() {
  imageLoading.value = false;
}

function onImageError() {
  imageLoading.value = false;
  console.error('Failed to load image');
}

function onGalleryImageLoad() {
  // Handle gallery image loading if needed
}

// Gallery navigation methods
function openGalleryViewer(index: number) {
  currentGalleryIndex.value = index;
  showGalleryViewer.value = true;
  galleryControlsVisible.value = true;
}

function closeGalleryViewer() {
  showGalleryViewer.value = false;
  currentGalleryIndex.value = 0;
}

function nextImage() {
  if (currentGalleryIndex.value < attachmentList.value.length - 1) {
    currentGalleryIndex.value++;
  }
}

function previousImage() {
  if (currentGalleryIndex.value > 0) {
    currentGalleryIndex.value--;
  }
}

function setCurrentGalleryIndex(index: number) {
  currentGalleryIndex.value = index;
}

function viewAllImages() {
  if (imageFiles.value.length > 0) {
    const firstImageIndex = attachmentList.value.findIndex(file => isImageFile(file.mimeType));
    openGalleryViewer(firstImageIndex >= 0 ? firstImageIndex : 0);
  }
}

function toggleGalleryControls() {
  galleryControlsVisible.value = !galleryControlsVisible.value;
}

// File operations
function viewFile(attachment: Attachment) {
  if (!attachment) {return;}
  viewingFile.value = attachment;
  showViewerDialog.value = true;

  // Load full image data if needed
  if (attachment.id && isImageFile(attachment.mimeType)) {
    loadImageSrc(attachment);
  }
}

function downloadFile(attachment: Attachment | null) {
  if (!attachment?.id) {return;}
  getFull(attachment.id, true);
}

function confirmDelete(attachment: Attachment) {
  fileToDelete.value = attachment;
  showDeleteDialog.value = true;
}

function deleteFile() {
  if (fileToDelete.value?.id) {
    deleteAttachment(fileToDelete.value.id);
  }
  showDeleteDialog.value = false;
  fileToDelete.value = null;
}

// File upload method
function uploadFile(file: File) {
  try {
    isUploading.value = true;
    emit('update:isLoading', true);

    const attachment: Attachment = new Attachment();
    attachment.documentTypeId = props.documentTypeId;

    const promise = getBase64(file);
    if (!promise) {
      resetComponent(true);
      return;
    }

    promise
      .then((result) => {
        const base64: any = result;
        const guid = uuidv4();
        const fileName: string = guid + '-' + file.name;
        attachment.data = base64;
        attachment.mimeType = file.type;
        attachment.name = fileName;
        attachment.size = file.size; // Add file size
        awaitingImageSaveFileName.value = guid.toLowerCase();

        if (props.isAccountRecovery) {
          emit('setAttachment', attachment);
        } else {
          saveAttachment(attachment);
        }
      })
      .catch((error) => {
        console.error('Error processing file:', error);
        resetComponent(true);
      });
  } catch (error) {
    console.error(error);
    resetComponent(true);
  }
}

function getBase64(file: File): Promise<string> | null {
  try {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        resolve(reader.result as string);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  } catch (error) {
    console.error(error);
    return null;
  }
}

function saveAttachments() {
  // This method is kept for backward compatibility
  // New file handling is done through processFiles/uploadFile
  if (!fileInput.value?.files) {
    resetComponent(true);
    return;
  }

  processFiles(fileInput.value.files);

  // Clear the file input
  if (fileInput.value) {
    fileInput.value.value = '';
  }
}

/**
 * Dispatches the save operation for the new Attachment document, and handles
 * the response
 * @param attachment Attachment object to be saved
 */
async function saveAttachment(attachment: Attachment) {
  const savedAttachment = await useAttachmentStore().saveAttachment(attachment);
  setSavedAttachment(savedAttachment);
}

/**
 * Handles response to a newly saved Attachment document
 * @param attachment the response to the save operation
 */
function setSavedAttachment(attachment: Attachment | null) {
  if (!awaitingImageSaveFileName.value) {
    return;
  }
  if (
    !attachment ||
    !attachment.name ||
    !attachment.name.toLowerCase().includes(awaitingImageSaveFileName.value)
  ) {
    showNotification(GENERIC_ERROR_MESSAGE, { title: 'File Upload' });
    isUploading.value = false;
    emit('update:isLoading', false);
    awaitingImageSaveFileName.value = null;
    return;
  }
  if (props.attachmentSingle && props.attachment) {
    props.attachment.id = attachment.id;
    props.attachment.documentTypeId = attachment.documentTypeId;
    props.attachment.mimeType = attachment.mimeType;
    props.attachment.data = attachment.data;
    props.attachment.name = attachment.name;
    props.attachment.timestamp = attachment.timestamp;
  } else {
    props.attachmentArray.push(attachment);
  }
  isUploading.value = false;
  emit('update:isLoading', false);
  awaitingImageSaveFileName.value = null;
}

function resetComponent(error: boolean) {
  if (error) {
    showNotification(GENERIC_ERROR_MESSAGE, { title: 'File Upload' });
  }
  isUploading.value = false;
  awaitingImageSaveFileName.value = null;
  emit('update:isLoading', false);
}

onMounted(() => {
  // Load the image source when the component is mounted
  if (props.attachment) {
    loadImageSrc(props.attachment);
  }
});
</script>

<style scoped lang="scss">
.professional-file-upload {
  // background: #1a1a1a;
  color: #ffffff;
  border-radius: 12px;
  // padding: 20px;
  &.upload-disabled {
    pointer-events: none;
    opacity: 0.6;
  }
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 20px;
  background: linear-gradient(135deg, #2d2d2d 0%, #1f1f1f 100%);
  border-radius: 12px;
  border: 1px solid #404040;

  .upload-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .upload-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .mode-chip {
      font-weight: 500;
      background: rgba(33, 150, 243, 0.2) !important;
      border-color: #2196f3 !important;
      color: #64b5f6 !important;
    }

    .file-count {
      font-size: 0.875rem;
      color: #b0b0b0;
      font-weight: 500;
    }
  }
}

.upload-progress {
  border-radius: 4px;
  overflow: hidden;
}

.drop-zone {
  border: 2px dashed #555555;
  border-radius: 16px;
  padding: 8px 22px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(33, 150, 243, 0.1), transparent);
    transition: left 0.5s;
  }

  &:hover:not(.drop-zone-disabled) {
    border-color: #2196f3;
    background: linear-gradient(135deg, #1e3a5f 0%, #1a2332 100%);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(33, 150, 243, 0.25);

    &::before {
      left: 100%;
    }

    .upload-main-icon {
      transform: scale(1.15);
      color: #64b5f6 !important;
    }
  }

  &.drop-zone-active {
    border-color: #4caf50;
    background: linear-gradient(135deg, #1b4332 0%, #2d5a3d 100%);
    transform: scale(1.03);
    box-shadow: 0 16px 40px rgba(76, 175, 80, 0.3);

    .upload-main-icon {
      color: #81c784 !important;
      transform: scale(1.25);
    }
  }

  &.drop-zone-disabled {
    cursor: not-allowed;
    opacity: 0.4;
    filter: grayscale(1);
  }
}

.drop-zone-content {
  position: relative;
  z-index: 1;
}

.upload-icon-container {
  .upload-main-icon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    &.rotating {
      animation: rotate 2s linear infinite;
    }
  }
}

.upload-text {
  .upload-primary-text {
    margin: 0 0 12px 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .upload-secondary-text {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    color: #b0b0b0;
  }

  .upload-hint {
    margin: 0;
    font-size: 0.8rem;
    color: #888888;
  }
}

// Unified File Gallery - Dark Theme
.unified-file-gallery {
  background: linear-gradient(135deg, #2d2d2d 0%, #1f1f1f 100%);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #404040;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.gallery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #404040;

  .gallery-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    display: flex;
    align-items: center;
  }

  .gallery-actions {
    .v-btn {
      border-color: #2196f3 !important;
      color: #64b5f6 !important;

      &:hover {
        background: rgba(33, 150, 243, 0.1) !important;
        transform: translateY(-1px);
      }
    }
  }
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.gallery-item {
  background: linear-gradient(135deg, #3a3a3a 0%, #2a2a2a 100%);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #505050;
  cursor: pointer;
  position: relative;

  &:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
    border-color: #2196f3;

    .file-overlay {
      opacity: 1;
    }

    .file-actions {
      transform: translateY(0);
      opacity: 1;
    }

    .gallery-item-overlay {
      opacity: 1;
    }
  }
}

.gallery-item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(33, 150, 243, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

// High-Quality Image Container
.high-quality-image-container {
  position: relative;
  width: 100%;
  height: 180px;
  overflow: hidden;
  border-radius: 8px;

  .high-quality-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;

    &:hover {
      transform: scale(1.05);
    }
  }

  .image-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// Enhanced File Icon Container
.enhanced-file-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 180px;
  background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
  position: relative;

  .icon-background {
    width: 80px;
    height: 80px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;

    &.pdf-icon {
      background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
    }

    &.image-icon {
      background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    }

    &.excel-icon {
      background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    }

    &.word-icon {
      background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    }

    &.text-icon {
      background: linear-gradient(135deg, #757575 0%, #424242 100%);
    }

    &.code-icon {
      background: linear-gradient(135deg, #ff9800 0%, #e65100 100%);
    }

    &.default-icon {
      background: linear-gradient(135deg, #9e9e9e 0%, #616161 100%);
    }

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    }
  }

  .enhanced-file-icon {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  }

  .file-type-badge {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

.file-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;

  .overlay-btn {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(4px);

    &:hover {
      background: white !important;
      transform: scale(1.1);
    }
  }
}

.file-info {
  padding: 16px;

  .file-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: #1a1a1a;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .file-details {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .file-type {
      font-size: 0.75rem;
      color: #666;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .file-size {
      font-size: 0.75rem;
      color: #999;
    }
  }
}

.file-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  transform: translateY(10px);
  opacity: 0;
  transition: all 0.3s ease;

  .v-btn {
    min-width: 36px;
    width: 36px;
    height: 36px;

    &:hover {
      transform: scale(1.1);
    }
  }
}

.thumbnail-container {
  display: flex;
  justify-content: center;
  gap: 8px;
  overflow-x: auto;
  padding: 8px 0;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.gallery-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  flex-shrink: 0;

  &.active {
    border-color: #2196f3;
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(33, 150, 243, 0.4);
  }

  &:hover:not(.active) {
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
  }
}

.thumbnail-image-wrapper,
.thumbnail-icon-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #333;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 0.7rem;
  padding: 2px 4px;
  border-radius: 4px 0 0 0;
}

.dark-theme-card {
  background: linear-gradient(135deg, #2d2d2d 0%, #1f1f1f 100%) !important;
  border: 1px solid #404040 !important;
}

.dark-theme-header {
  background: linear-gradient(135deg, #3a3a3a 0%, #2a2a2a 100%) !important;
  border-bottom: 1px solid #404040 !important;
  color: #ffffff !important;
}

.dark-theme-content {
  background: #1a1a1a !important;
}

.dark-theme-actions {
  background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%) !important;
  border-top: 1px solid #404040 !important;
}

.simple-file-viewer {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
}

.simple-image-viewer {
  width: 100%;
  text-align: center;
}

.simple-viewer-image {
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.simple-non-image-viewer {
  text-align: center;
  padding: 40px;
}

// Animations
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// File Info and Actions - Dark Theme
.file-info {
  padding: 16px;
  background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);

  .file-name {
    font-weight: 600;
    font-size: 0.9rem;
    color: #ffffff;
    margin-bottom: 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .file-details {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .file-type {
      font-size: 0.75rem;
      color: #64b5f6;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      background: rgba(33, 150, 243, 0.1);
      padding: 2px 8px;
      border-radius: 8px;
      border: 1px solid rgba(33, 150, 243, 0.3);
    }

    .file-size {
      font-size: 0.75rem;
      color: #b0b0b0;
      font-weight: 500;
    }
  }
}

.file-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #1f1f1f 0%, #0f0f0f 100%);
  border-top: 1px solid #404040;
  transform: translateY(10px);
  opacity: 0;
  transition: all 0.3s ease;

  .v-btn {
    min-width: 36px;
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(4px);

    &:hover {
      transform: scale(1.15);
      background: rgba(255, 255, 255, 0.2) !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    &.v-btn--disabled {
      opacity: 0.3;
    }
  }
}

// Enhanced Animations and Transitions
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.gallery-item {
  animation: slideInUp 0.3s ease-out;
}

// Enhanced Responsive Design
@media (max-width: 1200px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 14px;
  }
}

@media (max-width: 768px) {
  .professional-file-upload {
    padding: 16px;
  }

  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 12px;
  }

  .upload-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
  }

  .drop-zone {
    padding: 32px 20px;
  }

  .gallery-nav-btn {
    &.gallery-nav-prev { left: 10px; }
    &.gallery-nav-next { right: 10px; }
  }

  .unified-file-gallery {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 10px;
  }

  .high-quality-image-container,
  .enhanced-file-icon-container {
    height: 140px;
  }

  .drop-zone {
    padding: 24px 16px;
  }

  .upload-text .upload-primary-text {
    font-size: 1.1rem;
  }

  .browse-btn {
    width: 100%;
    margin-top: 8px;
  }

  .gallery-thumbnails {
    padding: 12px;
  }

  .gallery-thumbnail {
    width: 50px;
    height: 50px;
  }
}

// Accessibility Enhancements
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// High Contrast Mode Support
@media (prefers-contrast: high) {
  .professional-file-upload {
    border: 2px solid #ffffff;
  }

  .drop-zone {
    border-width: 3px;
  }

  .gallery-item {
    border-width: 2px;
  }
}

// Print Styles
@media print {
  .professional-file-upload {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  .gallery-nav-btn,
  .file-actions,
  .browse-btn {
    display: none !important;
  }
}
</style>
</style>
