<template>
  <v-layout
    row
    wrap
    class="app-bgcolor--400 app-bordercolor--600 app-borderside--a"
  >
    <v-flex md12>
      <v-layout justify-space-between align-center pa-2>
        <h3 class="subheader--light pa-0" style="line-height: 1">
          {{ equipmentTypeName }} Details
        </h3>
        <v-btn
          color="blue"
          small
          @click="addNewEquipmentItem"
          class="ma-0"
          :disabled="!isEdited"
        >
          Add {{ equipmentTypeName }}
        </v-btn>
      </v-layout>
      <v-divider></v-divider>
    </v-flex>
    <v-flex md12 pa-2>
      <div class="list-container mb-3">
        <v-data-table
          class="gd-dark-theme"
          :headers="headers"
          :items="equipmentDetails.straps"
          must-sort
          hide-actions
        >
          <template v-slot:items="props">
            <tr
              @click="viewEquipmentItem(props.item, props.index)"
              style="cursor: pointer"
            >
              <td class="text-xs-left">{{ props.item.serialNumber }}</td>
              <td class="text-xs-left">
                {{
                  props.item.inspectionDate
                    ? returnFormattedDate(props.item.inspectionDate)
                    : '-'
                }}
              </td>
              <td class="text-xs-right">
                {{ props.item.attachmentId ? 'YES' : 'NO' }}
              </td>
            </tr>
          </template>
        </v-data-table>
      </div>
      <v-dialog
        v-if="equipmentItem"
        v-model="maintenanceDialogIsOpen"
        content-class="v-dialog-custom"
        width="600px"
        persistent
      >
        <v-layout
          justify-space-between
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>{{ equipmentTypeName }} Maintenance</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="cancelMaintenance"
            :style="isLoading ? 'pointer-events: none;' : ''"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>

        <v-layout
          class="body-scrollable--65 app-theme__center-content--body dialog-content pa-3 body-min-height--65"
          row
          wrap
        >
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">1. Key Details</h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <span
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >Serial Number</span
                  >
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-text-field
                  :class="!isEdited ? 'solo-input-disable-display' : ''"
                  solo
                  flat
                  autofocus
                  color="light-blue"
                  :disabled="!isEdited"
                  class="v-solo-custom"
                  label=""
                  v-model="equipmentItem.serialNumber"
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <span
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >Latest Inspection Date</span
                  >
                </v-layout>
              </v-flex>
              <v-flex md8>
                <DateTimeInputs
                  :epochTime.sync="equipmentItem.inspectionDate"
                  :enableValidation="true"
                  :type="DateTimeType.DATE_START_OF_DAY"
                  dateLabel=""
                  :soloInput="true"
                  :boxInput="false"
                  :readOnly="!isEdited"
                  :hintTextType="HintTextType.FORMATTED_SELECTION"
                ></DateTimeInputs>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">
                2. Compliance Certificate
              </h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex
            md12
            v-if="
              attachment &&
              !attachment.id &&
              equipmentWithExistingCertificates.length > 0
            "
          >
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">
                    Apply Existing Certificate
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-select
                  label="Select Existing Certificate From Chain"
                  v-model="selectedCertificate"
                  :items="equipmentWithExistingCertificates"
                  item-text="serialNumber"
                  class="v-solo-custom"
                  solo
                  flat
                  item-value="attachmentId"
                  @input="addExistingCertificate"
                ></v-select>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Certificate</h6>
                </v-layout>
              </v-flex>
              <v-flex md6>
                <file-upload
                  :isLoading="isLoading"
                  @update:isLoading="(value) => (isLoading = value)"
                  :imageLabel="'COMPLIANCE CERTIFICATE'"
                  :attachmentSingle="true"
                  :documentTypeId="attachmentTypes.COMPLIANCE_CERTIFICATE"
                  :attachment="attachment"
                  :formDisabled="!isEdited"
                >
                </file-upload>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12>
            <v-divider class="mt-2"></v-divider>
            <v-layout justify-end align-end style="height: 100%">
              <ConfirmationDialog
                v-if="editingEquipmentItem"
                :buttonText="'Delete ' + equipmentTypeName"
                message="Please confirm that you wish to remove this equipment item."
                title="Removal Confirmation"
                @confirm="deleteEquipmentItem"
                :cancelButtonText="'cancel'"
                :isSmallButton="false"
                :buttonDisabled="!isEdited"
                :flat="true"
                :buttonColor="'error'"
                :confirmationButtonText="'Confirm and remove'"
                :dialogIsActive="true"
                :confirmationButtonColor="'error'"
                :cancelButtonColor="'blue'"
              ></ConfirmationDialog>
              <v-spacer></v-spacer>
              <v-btn
                @click="cancelMaintenance"
                color="error"
                outline
                :disabled="!isEdited"
                >cancel</v-btn
              >
              <v-btn
                @click="saveEdit"
                color="info"
                depressed
                :disabled="!isEdited || isLoading"
                v-if="editingEquipmentItem"
              >
                Update {{ equipmentTypeName }}
              </v-btn>
              <v-btn
                @click="saveEquipmentItem"
                color="info"
                depressed
                v-if="!editingEquipmentItem"
                :disabled="!isEdited || isLoading"
                >Add {{ equipmentTypeName }}</v-btn
              >
            </v-layout>
          </v-flex>
        </v-layout>
      </v-dialog>
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import FileUpload from '@/components/common/file-upload/file_upload.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import Straps from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/StrapDetails/AdditionalStrapObjects/Straps';
import StrapDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/StrapDetails/StrapDetails';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { computed, ComputedRef, ref, Ref } from 'vue';

const props = withDefaults(
  defineProps<{
    equipmentDetails: StrapDetails | any;
    equipmentTypeId: number;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const attachmentTypes = AttachmentTypes;
const attachment: Ref<Attachment | null> = ref(null);
const equipmentItem: Ref<Straps | null> = ref(null);
const editingEquipmentItem: Ref<boolean> = ref(false);
const isLoading: Ref<boolean> = ref(false);
const maintenanceDialogIsOpen: Ref<boolean> = ref(false);
const indexOfEditedItem: Ref<number> = ref(-1);
const selectedCertificate: Ref<string | null> = ref(null);

const headers: TableHeader[] = [
  {
    text: 'Identification',
    align: 'left',
    sortable: true,
    value: 'date',
  },
  {
    text: 'Inspection Date',
    align: 'left',
    value: 'result',
    sortable: false,
  },
  {
    text: 'Certificate',
    align: 'right',
    sortable: false,
    value: '',
  },
];

const equipmentTypeName: ComputedRef<string> = computed(() => {
  if (props.equipmentTypeId === 3) {
    return 'Strap';
  } else if (props.equipmentTypeId === 17) {
    return 'Lifting Chain';
  } else if (props.equipmentTypeId === 18) {
    return 'Lifting Sling ';
  }
  return '';
});

function addNewEquipmentItem(): void {
  equipmentItem.value = new Straps();
  setNewAttachment();
  maintenanceDialogIsOpen.value = true;
}

function saveEquipmentItem(): void {
  if (!equipmentItem.value) {
    return;
  }
  if (attachment.value && attachment.value.id) {
    // check if attachment already exists
    equipmentItem.value.attachmentId = attachment.value.id;
    const existingAttachment = props.equipmentDetails.attachments.find(
      (x: Attachment) => x.id === attachment.value!.id,
    );

    if (!existingAttachment) {
      props.equipmentDetails.attachments.push(attachment.value);
    }
  }
  props.equipmentDetails.straps.push(equipmentItem.value);
  equipmentItem.value = null;
  maintenanceDialogIsOpen.value = false;
  attachment.value = null;
  selectedCertificate.value = null;
}

const equipmentWithExistingCertificates: ComputedRef<Straps[]> = computed(
  () => {
    return props.equipmentDetails.straps.filter((x: Straps) => x.attachmentId);
  },
);

function addExistingCertificate(attachmentId: any) {
  if (!equipmentItem.value) {
    return;
  }
  const foundAttachment = props.equipmentDetails.attachments.find(
    (x: Attachment) => x.id === attachmentId,
  );

  if (foundAttachment) {
    equipmentItem.value.attachmentId = attachmentId;
    attachment.value = JSON.parse(JSON.stringify(foundAttachment));
  }
}

function saveEdit(): void {
  if (indexOfEditedItem.value === -1 || !equipmentItem.value) {
    return;
  }
  if (attachment.value && attachment.value.id) {
    // check if chains attachment was removed and replaced with another attachment
    if (
      props.equipmentDetails.straps[indexOfEditedItem.value].attachmentId !==
      attachment.value.id
    ) {
      // Find whether the attachment that was removed is assigned to any other chain.
      // set and remove the attachment from the chain that is to be updated
      const attachmentIdToCheck =
        props.equipmentDetails.straps[indexOfEditedItem.value].attachmentId;
      props.equipmentDetails.straps[indexOfEditedItem.value].attachmentId = '';
      // find chains that have the attachment that was removed from this chain
      const chainsWithAttachment: Straps[] =
        props.equipmentDetails.straps.filter(
          (x: Straps) => x.attachmentId === attachmentIdToCheck,
        );
      if (chainsWithAttachment.length === 0) {
        const attachmentIndexToCheck =
          props.equipmentDetails.attachments.findIndex(
            (item) => item.id === attachmentIdToCheck,
          );

        if (attachmentIndexToCheck !== -1) {
          props.equipmentDetails.attachments.splice(attachmentIndexToCheck, 1);
        }
      }
    }
    equipmentItem.value.attachmentId = attachment.value.id;
    const attachmentIndex = props.equipmentDetails.attachments.findIndex(
      (item) => item.id === attachment.value!.id,
    );

    if (attachmentIndex !== -1) {
      props.equipmentDetails.attachments.splice(
        attachmentIndex,
        1,
        attachment.value,
      );
    } else {
      props.equipmentDetails.attachments.push(attachment.value);
    }
  } else {
    if (equipmentItem.value.attachmentId) {
      const attachmentIndex = props.equipmentDetails.attachments.findIndex(
        (item) => item.id === equipmentItem.value!.attachmentId,
      );
      const chainsWithAttachment: Straps[] =
        props.equipmentDetails.straps.filter(
          (x: Straps) => x.attachmentId === equipmentItem.value!.attachmentId,
        );
      if (chainsWithAttachment.length === 1) {
        props.equipmentDetails.attachments.splice(attachmentIndex, 1);
      }
    }
    equipmentItem.value.attachmentId = '';
  }
  props.equipmentDetails.straps.splice(
    indexOfEditedItem.value,
    1,
    equipmentItem.value,
  );
  equipmentItem.value = null;
  maintenanceDialogIsOpen.value = false;
  editingEquipmentItem.value = false;
  indexOfEditedItem.value = -1;
  selectedCertificate.value = null;
}

function deleteEquipmentItem() {
  if (!equipmentItem.value) {
    return;
  }
  props.equipmentDetails.straps.splice(indexOfEditedItem.value, 1);
  const attachmentIndex = props.equipmentDetails.attachments.findIndex(
    (item) => item.id === equipmentItem.value!.attachmentId,
  );
  if (attachmentIndex !== -1) {
    // check that this attachment is applied to another chain
    const chainsWithAttachment: Straps[] = props.equipmentDetails.straps.filter(
      (x: Straps) => x.attachmentId === equipmentItem.value!.attachmentId,
    );
    if (chainsWithAttachment.length === 0) {
      props.equipmentDetails.attachments.splice(attachmentIndex, 1);
    }
  }
  maintenanceDialogIsOpen.value = false;
  equipmentItem.value = null;
  attachment.value = null;
  editingEquipmentItem.value = false;
  indexOfEditedItem.value = -1;
  selectedCertificate.value = null;
}

function viewEquipmentItem(chain: Straps, indexOfEditedChain: number) {
  equipmentItem.value = JSON.parse(JSON.stringify(chain));
  if (!equipmentItem.value) {
    return;
  }
  indexOfEditedItem.value = indexOfEditedChain;
  maintenanceDialogIsOpen.value = true;

  // check if chain has an attachment
  if (equipmentItem.value.attachmentId) {
    const attachmentIndex = props.equipmentDetails.attachments.findIndex(
      (item) => item.id === equipmentItem.value!.attachmentId,
    );
    if (attachmentIndex !== -1) {
      attachment.value = JSON.parse(
        JSON.stringify(props.equipmentDetails.attachments[attachmentIndex]),
      );
    } else {
      setNewAttachment();
    }
  } else {
    setNewAttachment();
  }
  editingEquipmentItem.value = true;
}

function setNewAttachment() {
  attachment.value = new Attachment();
  attachment.value.documentTypeId = attachmentTypes.COMPLIANCE_CERTIFICATE;
}

function cancelMaintenance() {
  maintenanceDialogIsOpen.value = false;
  equipmentItem.value = null;
  attachment.value = null;
  editingEquipmentItem.value = false;
  indexOfEditedItem.value = -1;
  selectedCertificate.value = null;
}
</script>

<style scoped lang="scss">
.selectedImage {
  border: 5px solid orange;
}

.gd-dark-theme {
  border: none !important;
}
</style>
