<template>
  <section class="fuel-surcharge-levy-details">
    <!-- Active rates summary slot -->
    <v-flex offset-md2 mb-4>
      <slot name="active-rates-summary"></slot>
    </v-flex>
    <TableTitleHeader>
      <!-- Title slot -->
      <template #title>
        <GTitle
          :title="`${title} Fuel Surcharges`"
          subtitle="Manage fuel surcharge rates"
          :divider="false"
        />
      </template>

      <!-- Buttons slot -->
      <template #buttons>
        <v-btn
          color="orange"
          @click="openBulkEditDialog"
          :disabled="selectedSurchargesForBulkEdit.length < 1"
        >
          <v-icon small class="pa-2">fas fa-edit</v-icon>
          Bulk Edit ({{ selectedSurchargesForBulkEdit.length }})
        </v-btn>

        <v-btn
          color="primary"
          @click="openCreateDialog"
          :disabled="!isAuthorised"
        >
          <v-icon small class="pa-2">fas fa-plus</v-icon>
          Create New
        </v-btn>
      </template>

      <!-- Inputs slot -->
      <template #inputs v-if="!props.entityId">
        <v-flex class="d-flex align-center">
          <v-text-field
            v-model="search"
            append-icon="search"
            label="Search Surcharges"
            solo
            flat
            class="v-solo-custom"
            dense
            hide-details
            single-line
          />
        </v-flex>

        <v-flex class="d-flex align-center">
          <h6 class="subheader--faded mb-0 mr-2">Filter:</h6>
        </v-flex>

        <v-flex class="d-flex align-center mr-4">
          <v-select
            v-model="selectedStatus"
            :items="statusFilterOptions"
            item-text="text"
            item-value="value"
            label="Filter by Status"
            solo
            flat
            dense
            hide-details
            single-line
            class="v-solo-custom"
            @change="loadRateVariations"
          />
        </v-flex>
      </template>
    </TableTitleHeader>

    <!-- Main data table with uuid-based grouping -->
    <div class="fuel-surcharge-table-container mt-4">
      <v-data-table
        :headers="tableHeaders"
        :items="groupedFuelSurchargeRates"
        item-key="uuid"
        hide-actions
        no-data-text="No fuel surcharges found"
        class="table-with-expansion"
        :expand="false"
        :loading="isLoading"
      >
        <template v-slot:items="data">
          <v-progress-circular
            v-if="data.item.isLoading"
            indeterminate
            color="accent"
            size="24"
            class="mr-2"
          ></v-progress-circular>
          <tr
            v-else
            @click="toggleGroupExpansion(data)"
            :style="{
              cursor: 'pointer',
            }"
            :class="{
              'group-expanded': data.expanded,
              'active-surcharge': data.item.isActive,
              'loading-group': data.item.isLoading,
            }"
          >
            <td></td>
            <td>{{ data.item.name }}</td>
            <td>
              <span v-if="!data.expanded">
                {{ data.item.rateDisplay }}
              </span>
            </td>
            <td>
              <span v-if="!data.expanded">
                {{ returnFormattedDate(data.item.earliestValidFrom) }}
              </span>
            </td>
            <td>
              <span v-if="!data.expanded">
                {{ returnFormattedDate(data.item.latestValidTo) }}
              </span>
            </td>

            <td>
              <div
                v-if="!data.expanded"
                class="d-flex flex-column align-center"
              >
                <span class="caption text--secondary mt-1">
                  {{ formatGroupSurchargeLevel(data.item) }}
                </span>
              </div>
            </td>
            <td>
              <v-icon
                class="expansion-icon"
                :class="{ expanded: data.expanded }"
              >
                fal fa-chevron-down
              </v-icon>
            </td>
          </tr>
        </template>

        <template v-slot:expand="data">
          <div v-if="data.expanded" class="expanded-content">
            <div>
              <div class="expanded-header pa-3">
                <v-layout align-center row wrap>
                  <v-checkbox
                    :key="`group-${data.item.uuid}-${selectionStateTracker}`"
                    :label="`Select All (${data.item.surcharges.length})`"
                    :input-value="isGroupFullySelected(data.item.surcharges)"
                    :indeterminate="
                      isGroupPartiallySelected(data.item.surcharges)
                    "
                    @click.stop
                    @change="
                      toggleAllSurchargesInGroup(data.item.surcharges, $event)
                    "
                    hide-details
                    :ripple="false"
                    color="info"
                    class="ml-1"
                    :disabled="!canEditSurchargeGroup(data.item.surcharges)"
                  ></v-checkbox>
                  <v-btn
                    v-if="canEditSurchargeGroup(data.item.surcharges)"
                    outline
                    small
                    dense
                    solo
                    class="ma-0 mr-4"
                    color="orange"
                    rounded
                    @click="
                      toggleAllSurchargesInGroup(data.item.surcharges, true),
                        openBulkEditDialog(),
                        setBulkEditLevel(data.item)
                    "
                  >
                    <v-icon small class="pr-2">edit</v-icon>
                    Bulk Edit</v-btn
                  >
                  <v-btn
                    outline
                    small
                    dense
                    solo
                    class="ma-0 mr-4"
                    rounded
                    @click="addToGroupSurcharge(data.item.uuid)"
                    :disabled="!isAuthorised"
                  >
                    <v-icon small class="pr-2">add</v-icon>
                    Add to group</v-btn
                  >
                </v-layout>
              </div>
              <v-data-table
                :headers="tableHeaders"
                :items="data.item.surcharges"
                item-key="tableId"
                hide-actions
                hide-headers
                class="gd-dark-theme ml-4 mb-4"
                :loading="data.item.isLoading"
              >
                <template v-slot:items="detailProps">
                  <tr>
                    <td>
                      <v-checkbox
                        :key="`item-${detailProps.item.tableId}-${detailProps.item.isSelected}`"
                        @click.stop
                        v-model="detailProps.item.isSelected"
                        @change="updateSelectionArrays()"
                        hide-details
                        color="info"
                        class="mt-0"
                        :ripple="false"
                        :disabled="!canEditSurcharge(detailProps.item)"
                      ></v-checkbox>
                    </td>
                    <td class="inner-table-name">
                      {{ detailProps.item.name }}
                    </td>
                    <td>
                      <span
                        small
                        class="ml-4"
                        :class="
                          isActive(
                            detailProps.item.validFromDate,
                            detailProps.item.validToDate,
                          )
                            ? 'success'
                            : 'default'
                        "
                      >
                        {{
                          isActive(
                            detailProps.item.validFromDate,
                            detailProps.item.validToDate,
                          )
                            ? 'Active'
                            : 'Inactive'
                        }}
                      </span>
                    </td>
                    <td class="inner-table-rate">
                      {{ formatRateDisplay(detailProps.item) }}
                    </td>
                    <td class="inner-table-valid-from">
                      {{ returnFormattedDate(detailProps.item.validFromDate) }}
                    </td>
                    <td class="inner-table-valid-to">
                      {{ returnFormattedDate(detailProps.item.validToDate) }}
                    </td>
                    <td class="inner-table-status">
                      <div class="d-flex flex-column align-start">
                        <span class="caption text--secondary">
                          {{
                            formatServiceTypesDisplay(
                              detailProps.item.serviceTypes,
                            )
                          }}
                        </span>
                      </div>
                    </td>
                    <td
                      v-if="
                        !props.entityId &&
                        detailProps.item.clientIds &&
                        !detailProps.item.clientIds.includes('0')
                      "
                      style="cursor: pointer"
                    >
                      <v-tooltip bottom>
                        <template #activator="{ on, attrs }">
                          <span v-bind="attrs" v-on="on">
                            {{ detailProps.item.clientIds.length }} Clients
                          </span>
                        </template>
                        <span>
                          {{ getClientNames(detailProps.item.clientIds) }}
                        </span>
                      </v-tooltip>
                    </td>
                    <td
                      v-if="
                        !props.entityId &&
                        detailProps.item.fleetAssetIds &&
                        !detailProps.item.fleetAssetIds.includes('0')
                      "
                    >
                      {{ detailProps.item.fleetAssetIds.length }}
                      Fleet Assets
                    </td>
                    <td class="inner-table-actions">
                      <v-flex v-if="canEditSurcharge(detailProps.item)">
                        <v-btn
                          icon
                          small
                          @click="editFuelSurcharge(detailProps.item)"
                        >
                          <v-icon small color="accent">fas fa-edit</v-icon>
                        </v-btn>
                      </v-flex>

                      <!-- Multi-client message - show when editing is disabled -->
                      <div v-else>
                        <v-menu bottom v-if="isAuthorised">
                          <template v-slot:activator="{ on: menu }">
                            <v-tooltip bottom>
                              <template v-slot:activator="{ on: tooltip }">
                                <v-btn flat icon v-on="{ ...tooltip, ...menu }">
                                  <v-icon color="accent" size="16"
                                    >fas fa-ellipsis-h</v-icon
                                  >
                                </v-btn>
                              </template>
                              <span>View Actions</span>
                            </v-tooltip>
                          </template>
                          <v-list dense two-line class="v-list-custom">
                            <v-list-tile
                              @click="navigateToDivisionRateVariations"
                            >
                              <v-list-tile-title>
                                <v-icon
                                  small
                                  size="10"
                                  color="accent"
                                  class="mr-2"
                                  >fas fa-edit</v-icon
                                >
                                Includes more than one client, Edit at division
                                level
                              </v-list-tile-title>
                            </v-list-tile>
                            <v-list-tile
                              @click="
                                removeEntityFromFuelLevy(detailProps.item)
                              "
                            >
                              <v-list-tile-title>
                                <v-icon
                                  size="10"
                                  small
                                  color="error"
                                  class="mr-2"
                                  >fas fa-trash</v-icon
                                >
                                Remove current
                                {{
                                  componentType ===
                                  FuelComponentType.FLEET_ASSET
                                    ? 'fleet asset'
                                    : 'client'
                                }}
                                from fuel surcharge
                              </v-list-tile-title>
                            </v-list-tile>
                          </v-list>
                        </v-menu>
                      </div>
                    </td>
                  </tr>
                </template>
              </v-data-table>
            </div>
          </div>
        </template>
      </v-data-table>
    </div>

    <!-- Dialogs Component -->
    <FuelSurchargeLevyDialogs
      :componentType="props.componentType"
      :entityId="props.entityId"
      :showCreateEditDialog="showCreateEditDialog"
      :showBulkEditDialog="showBulkEditDialog"
      :showDateAdjustmentDialog="showDateAdjustmentDialog"
      :selectedSurchargesForBulkEdit="selectedSurchargesForBulkEdit"
      :isEditing="isEditing"
      :allFuelSurchargeRates="allFuelSurchargeRates"
      :currentEditingSurcharge="currentEditingSurcharge"
      :groupCount="groupCount"
      :existingGroupOptions="existingGroupOptions"
      :availableServiceTypes="availableServiceTypes"
      :availableClients="availableClients"
      :addToGroupUuId="addToGroupUuId"
      :bulkEditLevel="bulkEditLevel"
      :availableFleetAssets="availableFleetAssets"
      @update:showCreateEditDialog="
        (showCreateEditDialog = $event),
          (currentEditingSurcharge = null),
          (addToGroupUuId = '')
      "
      @update:showBulkEditDialog="handleBulkEditDialogClose"
      @refreshData="loadRateVariations"
      @refreshGroupData="forceRefreshGroups"
    />
  </section>
</template>

<script setup lang="ts">
import { AdministrationViewEnum } from '@/components/admin/Administration/AdministrationViewEnum';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { requestUserConfirmation } from '@/helpers/NotificationHelpers/ConfirmationHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { returnServiceTypeShortNameFromId } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import { ValidityStatus } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ValidityStatus';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import { FuelComponentType } from '@/interface-models/ServiceRates/FuelSurcharge/FuelComponentType';
import FuelSurchargeRate from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeRate';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import moment from 'moment-timezone';
import { computed, ComputedRef, onMounted, ref, Ref } from 'vue';
import { useRouter } from 'vue-router/composables';
import FuelSurchargeLevyDialogs from './fuel_surcharge_levy_dialogs.vue';
/**
 * Extended types to include selection properties for table row selection
 */
type FuelSurchargeRateSelection = FuelSurchargeRate & {
  isSelected: boolean;
};
/**
 * Interface for grouped fuel surcharge data used in the table display
 */
interface GroupedFuelSurcharge {
  uuid: string;
  name: string;
  rateDisplay: string;
  earliestValidFrom: number;
  latestValidTo: number;
  surcharges: FuelSurchargeRateSelection[];
  isLoading: boolean;
}

const props = withDefaults(
  defineProps<{
    componentType: FuelComponentType;
    entityId?: string;
  }>(),
  {
    entityId: '',
  },
);

const emit = defineEmits<{
  (event: 'refreshServiceRateList'): void;
}>();

const fuelLevyStore = useFuelLevyStore();
const clientDetailsStore = useClientDetailsStore();
const companyDetailsStore = useCompanyDetailsStore();
const fleetAssetStore = useFleetAssetStore();
const router = useRouter();

const search: Ref<string> = ref('');

const selectedIndividualSurcharges: Ref<string[]> = ref([]);
const expandedGroupsForSearch: Ref<string[]> = ref([]);
const loadingGroups: Ref<string[]> = ref([]);
const allFuelSurchargeRates: Ref<FuelSurchargeRate[]> = ref([]);
const showCreateEditDialog: Ref<boolean> = ref(false);
const showBulkEditDialog: Ref<boolean> = ref(false);
const showDateAdjustmentDialog: Ref<boolean> = ref(false);
const isEditing: Ref<boolean> = ref(false);
const isLoading: Ref<boolean> = ref(true);

const bulkEditLevel: Ref<string> = ref('');
const groupCount: Ref<number> = ref(0);
const addToGroupUuId: Ref<string> = ref('');

const selectedStatus: Ref<ValidityStatus> = ref(
  ValidityStatus.CURRENT_AND_FUTURE,
);

const currentEditingSurcharge: Ref<FuelSurchargeRate | null> = ref(null);

/** Map of loaded group surcharges by UUID */
const loadedGroupSurcharges: Ref<Map<string, FuelSurchargeRateSelection[]>> =
  ref(new Map());

const availableServiceTypes: ComputedRef<ServiceTypes[]> = computed(() => {
  return companyDetailsStore.getServiceTypesList.filter(
    (serviceType: ServiceTypes) =>
      serviceType.serviceTypeId !== 4 && serviceType.divisionService,
  );
});

const availableClients: ComputedRef<ClientSearchSummary[]> = computed(() => {
  return clientDetailsStore.clientSummaryList || [];
});

const availableFleetAssets: ComputedRef<FleetAssetSummary[]> = computed(() => {
  return fleetAssetStore.getAllFleetAssetList || [];
});

const isAuthorised: ComputedRef<boolean> = computed(() => {
  return hasAdminOrHeadOfficeRole();
});

const selectedSurchargesForBulkEdit: Ref<FuelSurchargeRate[]> = ref([]);

const selectionStateTracker: Ref<number> = ref(0);

/**
 * Determines if lazy loading should be used based on component type and entityId
 * Use lazy loading when there's no specific entityId provided
 */
const shouldUseLazyLoading: ComputedRef<boolean> = computed(() => {
  return !props.entityId;
});

const title = computed(() => {
  if (props.entityId) {
    return '';
  } else if (props.componentType === FuelComponentType.CASH_SALE) {
    return 'Cash Sales';
  } else if (props.componentType === FuelComponentType.FLEET_ASSET) {
    return 'Fleet Asset';
  } else {
    return 'Client';
  }
});

/**
 * Table headers configuration for the main data table
 */
const tableHeaders: TableHeader[] = [
  {
    text: '',
    align: 'left',
    value: 'data-table-select',
    sortable: false,
  },
  {
    text: 'Name',
    align: 'left',
    value: 'name',
    sortable: false,
    width: '380px',
  },
  {
    text: 'Rate',
    align: 'left',
    value: 'rateDisplay',
    sortable: false,
  },
  {
    text: 'Valid From',
    align: 'left',
    value: 'earliestValidFrom',
    sortable: false,
  },
  {
    text: 'Expires',
    align: 'left',
    value: 'latestValidTo',
    sortable: false,
  },
  {
    text: '',
    align: 'left',
    value: 'appliedToDisplay',
    sortable: false,
  },
  {
    text: '',
    align: 'left',
    value: 'actions',
    sortable: false,
  },
];

/**
 * Options for status filter selection
 */
const statusFilterOptions = [
  { text: 'All', value: ValidityStatus.ALL },
  { text: 'Current', value: ValidityStatus.CURRENT },
  { text: 'Expired', value: ValidityStatus.EXPIRED },
  { text: 'Future', value: ValidityStatus.FUTURE },
  { text: 'Current & Future', value: ValidityStatus.CURRENT_AND_FUTURE },
];

/**
 * Computed property that groups fuel surcharge rates by UUID and provides display data
 * Handles both lazy loading and immediate data contexts
 */
const groupedFuelSurchargeRates: ComputedRef<GroupedFuelSurcharge[]> = computed(
  () => {
    const groups = new Map<string, FuelSurchargeRateSelection[]>();
    // Group surcharges by uuid
    allFuelSurchargeRates.value.forEach((surcharge) => {
      if (!groups.has(surcharge.uuid)) {
        groups.set(surcharge.uuid, []);
      }
      groups.get(surcharge.uuid)!.push(surcharge as FuelSurchargeRateSelection);
    });

    // Convert to grouped format
    const result: GroupedFuelSurcharge[] = Array.from(groups.entries()).map(
      ([uuid, groupSurcharges]) => {
        const firstSurcharge: FuelSurchargeRateSelection = groupSurcharges[0];

        // Calculate group-level min/max dates
        const validFromDates = groupSurcharges
          .map((s) => s.validFromDate || 0)
          .filter((date) => date > 0);
        const validToDates = groupSurcharges
          .map((s) => s.validToDate || 0)
          .filter((date) => date > 0);

        const earliestValidFrom =
          validFromDates.length > 0 ? Math.min(...validFromDates) : 0;
        const latestValidTo =
          validToDates.length > 0 ? Math.max(...validToDates) : 0;

        // Handle lazy vs non-lazy contexts
        let isLoading: boolean;
        let displaySurcharges: FuelSurchargeRateSelection[];

        if (shouldUseLazyLoading.value) {
          const loadedSurcharges = loadedGroupSurcharges.value.get(uuid) ?? [];
          isLoading = loadingGroups.value.includes(uuid);

          displaySurcharges = [...loadedSurcharges].sort((a, b) => {
            //  sort surcharges inside each group
            const aActive = isActive(a.validFromDate, a.validToDate);
            const bActive = isActive(b.validFromDate, b.validToDate);

            if (aActive && !bActive) {
              return -1;
            }
            if (!aActive && bActive) {
              return 1;
            }

            return (b.validToDate || 0) - (a.validToDate || 0);
          });
        } else {
          isLoading = false;
          displaySurcharges = groupSurcharges;
        }

        return {
          uuid,
          name: firstSurcharge.name,
          rateDisplay: formatRateDisplay(firstSurcharge),
          earliestValidFrom,
          latestValidTo,
          isLoading,
          surcharges: displaySurcharges,
        };
      },
    );

    // Apply search filter
    const searchTerm = search.value.toLowerCase().trim();
    let filteredResult = result;

    if (searchTerm.length > 0) {
      filteredResult = result.filter((group) => {
        const groupNameMatches = group.name.toLowerCase().includes(searchTerm);

        let individualMatches = false;
        if (!group.isLoading) {
          individualMatches = group.surcharges.some((surcharge) =>
            surcharge.name.toLowerCase().includes(searchTerm),
          );

          if (
            individualMatches &&
            !expandedGroupsForSearch.value.includes(group.uuid)
          ) {
            expandedGroupsForSearch.value.push(group.uuid);
          }
        }

        return groupNameMatches || individualMatches;
      });
    }
    return filteredResult.sort((a, b) => {
      // Get the first surcharge from each group using the original data
      const aFirstSurcharge = allFuelSurchargeRates.value.find(
        (s) => s.uuid === a.uuid,
      );
      const bFirstSurcharge = allFuelSurchargeRates.value.find(
        (s) => s.uuid === b.uuid,
      );

      const aIsDivision = aFirstSurcharge
        ? isDivisionRate(aFirstSurcharge)
        : false;
      const bIsDivision = bFirstSurcharge
        ? isDivisionRate(bFirstSurcharge)
        : false;

      if (aIsDivision && !bIsDivision) {
        return -1;
      }
      if (!aIsDivision && bIsDivision) {
        return 1;
      }
      // Sort by date
      return (a.latestValidTo ?? -Infinity) - (b.latestValidTo ?? -Infinity);
    });
  },
);

// /**
//  * Options for existing group selection in "Add to Existing" tab
//  */
const existingGroupOptions: ComputedRef<{ uuid: string; name: string }[]> =
  computed(() => {
    let filteredGroups = groupedFuelSurchargeRates.value;

    // When entityId is present, filter groups to only show those associated with the specific entity
    if (props.entityId) {
      filteredGroups = groupedFuelSurchargeRates.value.filter((group) => {
        // Find the first surcharge in the group to check its associations
        const firstSurcharge = allFuelSurchargeRates.value.find(
          (surcharge) => surcharge.uuid === group.uuid,
        );

        if (!firstSurcharge) {
          return false;
        }

        // Check if the group is associated with the current entity
        switch (props.componentType) {
          case FuelComponentType.CLIENT:
            if (firstSurcharge instanceof ClientFuelSurchargeRate) {
              return firstSurcharge.clientIds.includes(props.entityId);
            }
            return false;

          case FuelComponentType.FLEET_ASSET:
            if (firstSurcharge instanceof FleetAssetFuelSurchargeRate) {
              return firstSurcharge.fleetAssetIds.includes(props.entityId);
            }
            return false;

          default:
            return true;
        }
      });
    }

    return filteredGroups.map((group) => ({
      uuid: group.uuid,
      name: group.name,
    }));
  });

function setBulkEditLevel(item: GroupedFuelSurcharge) {
  bulkEditLevel.value = formatGroupSurchargeLevel(item);
}

/**
 * Navigate to the division-level fuel surcharge variations page
 */
function navigateToDivisionRateVariations(): void {
  // set administration view to fleet/client fuel surcharge
  const view: AdministrationViewEnum =
    props.componentType === FuelComponentType.FLEET_ASSET
      ? AdministrationViewEnum.FAFSL
      : AdministrationViewEnum.FSL;
  useAppNavigationStore().setCurrentDivisionView(view);
  // Navigate to the administration
  router.push('/administration');
}

/**
 * Remove the current client from a fuel surcharge
 * @param surcharge - The surcharge to remove the client/FleetAsset from
 */
async function removeEntityFromFuelLevy(
  surcharge: FuelSurchargeRate,
): Promise<void> {
  if (!props.entityId || !surcharge.id) {
    return;
  }

  const ok = await requestUserConfirmation(
    `Are you sure you want to  Remove current ${
      props.componentType === FuelComponentType.FLEET_ASSET
        ? 'fleet asset'
        : 'client'
    } from fuel surcharge?`,
    'Delete Operations Channel',
  );

  if (!ok) {
    return;
  }

  isLoading.value = true;
  let result: FuelSurchargeRate | null = null;
  try {
    // Create updated surcharge with client removed
    const updatedSurcharge = { ...surcharge };
    if ('clientIds' in updatedSurcharge) {
      updatedSurcharge.clientIds = (
        surcharge as ClientFuelSurchargeRate
      ).clientIds.filter(function (id) {
        return id !== props.entityId;
      });
      // Save the updated surcharge
      result = await fuelLevyStore.saveClientFuelSurchargeRate(
        updatedSurcharge as ClientFuelSurchargeRate,
      );
    }

    // Create updated surcharge with fleet removed
    if ('fleetAssetIds' in updatedSurcharge) {
      updatedSurcharge.fleetAssetIds = (
        surcharge as FleetAssetFuelSurchargeRate
      ).fleetAssetIds.filter(function (id) {
        return id !== props.entityId;
      });
      // Save the updated surcharge
      result = await fuelLevyStore.saveFleetAssetFuelSurchargeRate(
        updatedSurcharge as FleetAssetFuelSurchargeRate,
      );
    }

    if (result) {
      showNotification('Client removed from fuel surcharge successfully', {
        title: 'Fuel Surcharges',
        type: HealthLevel.SUCCESS,
      });
      allFuelSurchargeRates.value = [];
      await fetchFuelSurchargeData();
    }
  } catch (error) {
    showNotification('Failed to remove client from fuel surcharge', {
      title: 'Fuel Surcharges',
      type: HealthLevel.ERROR,
    });
  } finally {
    isLoading.value = false;
  }
}

/**
 * Manually update the selection arrays based on current isSelected states
 * This ensures immediate reactivity when selections change
 */
function updateSelectionArrays(): void {
  // Collect all surcharges from all groups
  const allSurchargesFlat: FuelSurchargeRateSelection[] = [];

  groupedFuelSurchargeRates.value.forEach((group) => {
    allSurchargesFlat.push(...group.surcharges);
  });

  const selectedSurcharges = allSurchargesFlat.filter(
    (surcharge) => surcharge.isSelected,
  );

  // Update the selectedIndividualSurcharges array for compatibility
  selectedIndividualSurcharges.value = selectedSurcharges
    .map((surcharge) => surcharge.tableId.toString())
    .filter((id) => id !== '');

  // Update the selectedSurchargesForBulkEdit ref directly
  selectedSurchargesForBulkEdit.value = selectedSurcharges;

  // Force reactivity update
  selectionStateTracker.value++;
}

/**
 * Update selection arrays directly from a specific set of surcharges
 * This bypasses the allSurcharges computed property when we know the exact surcharges
 */
function updateSelectionArraysFromSurcharges(
  surcharges: FuelSurchargeRateSelection[],
  isSelected: boolean,
): void {
  if (isSelected) {
    // Add these surcharges to the selection
    const newSelectedSurcharges = surcharges.filter((s) => s.isSelected);
    const newIds = newSelectedSurcharges.map((s) => s.tableId.toString());

    // Add to existing selections (avoid duplicates)
    const existingIds = selectedIndividualSurcharges.value;
    const uniqueNewIds = newIds.filter((id) => !existingIds.includes(id));
    selectedIndividualSurcharges.value.push(...uniqueNewIds);

    // Add to bulk edit selection (avoid duplicates)
    const existingSurcharges = selectedSurchargesForBulkEdit.value;
    const uniqueNewSurcharges = newSelectedSurcharges.filter(
      (newSurcharge) =>
        !existingSurcharges.some(
          (existing) => existing.tableId === newSurcharge.tableId,
        ),
    );
    selectedSurchargesForBulkEdit.value.push(...uniqueNewSurcharges);
  } else {
    // Remove these surcharges from the selection
    const idsToRemove = surcharges.map((s) => s.tableId.toString());

    // Remove from individual selections
    selectedIndividualSurcharges.value =
      selectedIndividualSurcharges.value.filter(
        (id) => !idsToRemove.includes(id),
      );

    // Remove from bulk edit selection
    selectedSurchargesForBulkEdit.value =
      selectedSurchargesForBulkEdit.value.filter(
        (surcharge) => !idsToRemove.includes(surcharge.tableId.toString()),
      );
  }

  // Force reactivity update
  selectionStateTracker.value++;
}

/**
 * Formats service types for display in the inner table
 * @param serviceTypes - Array of service type IDs or null for all services
 * @returns Formatted service types display string
 */
function formatServiceTypesDisplay(
  serviceTypes: number[] | null | undefined,
): string {
  if (!serviceTypes || serviceTypes.length === 0) {
    return 'All Services';
  }

  const serviceTypeNames = serviceTypes
    .map((id) => returnServiceTypeShortNameFromId(id, 'Unknown'))
    .filter((name) => name !== 'Unknown');

  if (serviceTypeNames.length === 0) {
    return 'All Services';
  }

  return serviceTypeNames.join(', ');
}

/**
 * Formats the rate display string for a fuel surcharge
 * @param surcharge - The fuel surcharge to format
 * @returns Formatted rate display string (e.g., "5.5%" or "3.0% - 7.5%")
 */
function formatRateDisplay(surcharge: FuelSurchargeRate): string {
  if (surcharge.rateBrackets && surcharge.rateBrackets.length > 0) {
    const firstBracket = surcharge.rateBrackets[0];
    if (surcharge.rateBrackets.length === 1) {
      // Constant rate
      return `${firstBracket.rate}%`;
    } else {
      // Ranged rate - show range
      return `${firstBracket.rate}% - ${
        surcharge.rateBrackets[surcharge.rateBrackets.length - 1].rate
      }%`;
    }
  }
  return 'N/A';
}

/**
 * Determines if a date range is currently active
 * @param validFrom - Valid from date (epoch timestamp)
 * @param validTo - Valid to date (epoch timestamp)
 * @returns True if the date range is currently active
 */
function isActive(validFrom: number | null, validTo: number | null): boolean {
  const now = moment().valueOf();
  return (validFrom || 0) <= now && (validTo || Number.MAX_SAFE_INTEGER) >= now;
}

/**
 * Determines if a surcharge is a division-level rate
 * Division rates are where clientIds/fleetAssetIds === ['0'] and exactly one element
 * @param surcharge - The surcharge to check
 * @returns True if the surcharge is division-level
 */
function isDivisionRate(surcharge: FuelSurchargeRate): boolean {
  if (surcharge instanceof ClientFuelSurchargeRate) {
    return surcharge.clientIds.length === 1 && surcharge.clientIds[0] === '0';
  } else if (surcharge instanceof FleetAssetFuelSurchargeRate) {
    return (
      surcharge.fleetAssetIds.length === 1 && surcharge.fleetAssetIds[0] === '0'
    );
  }
  return false;
}

/**
 * Get formatted client names from client IDs
 * @param clientIds - Array of client IDs
 * @param showAll - Whether to show all client names or truncate with count
 * @returns Formatted client names string
 */
function getClientNames(clientIds: string[], showAll: boolean = false): string {
  if (!clientIds || clientIds.length === 0) {
    return 'No clients';
  }

  const clientNames = clientIds.map((clientId) => {
    // Convert to string in case it's a number
    const idStr = String(clientId);
    const client = availableClients.value.find((c) => c.clientId === idStr);
    return client
      ? client.clientDisplayName || client.clientName
      : `Client ${idStr}`;
  });

  if (showAll || clientNames.length <= 2) {
    return clientNames.join(', ');
  } else {
    return `${clientNames[0]} + ${clientNames.length - 1} more`;
  }
}

/**
 * Determines if a group can be edited based on user authorization and context
 * @param group - The grouped fuel surcharge to check
 * @returns True if the group can be edited
 */
function canEditSurcharge(surcharge: FuelSurchargeRate): boolean {
  if (!isAuthorised.value) {
    return false;
  }

  // If no entityId given, allow editing
  if (!props.entityId) {
    return true;
  }

  switch (props.componentType) {
    case FuelComponentType.CLIENT:
      if ('clientIds' in surcharge) {
        return (
          Array.isArray(surcharge.clientIds) &&
          surcharge.clientIds.length === 1 &&
          surcharge.clientIds[0] === props.entityId
        );
      }
      return true; // non-client surcharge in client context → allow

    case FuelComponentType.FLEET_ASSET:
      if ('fleetAssetIds' in surcharge) {
        return (
          Array.isArray(surcharge.fleetAssetIds) &&
          surcharge.fleetAssetIds.length === 1 &&
          surcharge.fleetAssetIds[0] === props.entityId
        );
      }
      return true;

    default:
      // Division, Cash Sale, Fleet Division contexts allow editing
      return true;
  }
}
/**
 * Determines if a group of surcharges can be edited
 * @param surcharges - Array of ClientFuelSurchargeRate or FleetAssetFuelSurchargeRate
 * @returns True if ALL surcharges in the array can be edited
 */
function canEditSurchargeGroup(surcharges: FuelSurchargeRate[]): boolean {
  return surcharges.every((s) => canEditSurcharge(s));
}

/**
 * Checks if all surcharges in a group are selected
 * @param surcharges - Array of surcharges in the group to check
 * @returns True if all surcharges are selected, false otherwise
 */
function isGroupFullySelected(
  surcharges: FuelSurchargeRateSelection[],
): boolean {
  if (surcharges.length === 0) {
    return false;
  }
  return surcharges.every((surcharge) => surcharge.isSelected);
}

/**
 * Checks if some (but not all) surcharges in a group are selected
 * @param surcharges - Array of surcharges in the group to check
 * @returns True if some surcharges are selected but not all, false otherwise
 */
function isGroupPartiallySelected(
  surcharges: FuelSurchargeRateSelection[],
): boolean {
  if (surcharges.length === 0) {
    return false;
  }
  const selectedCount = surcharges.filter(
    (surcharge) => surcharge.isSelected,
  ).length;
  return selectedCount > 0 && selectedCount < surcharges.length;
}

/**
 * Toggle selection state for all surcharges in a group
 * @param surcharges - Array of surcharges to toggle
 * @param isSelected - New selection state
 */
function toggleAllSurchargesInGroup(
  surcharges: FuelSurchargeRateSelection[],
  isSelected: boolean,
): void {
  // Set isSelected property on all surcharges in the group
  surcharges.forEach((surcharge) => {
    surcharge.isSelected = isSelected;
  });

  // Directly update the selection arrays using the surcharges we just modified
  updateSelectionArraysFromSurcharges(surcharges, isSelected);
}

/**
 * Clears all surcharge selections across all groups
 * @param excludeGroupUuid - Optional UUID of group to exclude from clearing
 */
function clearAllSurchargesSelection(excludeGroupUuid?: string): void {
  // Iterate through all groups and their surcharges
  groupedFuelSurchargeRates.value.forEach((group) => {
    group.surcharges.forEach((surcharge) => {
      // If excludeGroupUuid is provided, only clear selections from other groups
      if (!excludeGroupUuid || surcharge.uuid !== excludeGroupUuid) {
        surcharge.isSelected = false;
      }
    });
  });

  // Use the helper function to update all selection arrays
  updateSelectionArrays();
}

/**
 * Opens the create dialog for new fuel surcharge creation
 * Resets all form data and sets up initial state
 */
function openCreateDialog(): void {
  isEditing.value = false;
  currentEditingSurcharge.value = null;
  showCreateEditDialog.value = true;
}

/**
 * Opens the edit dialog for a single individual surcharge
 * Populates the form with the surcharge's current data for editing
 * @param surcharge - The individual surcharge to edit
 */
function editFuelSurcharge(surcharge: FuelSurchargeRate): void {
  // Set single item edit mode
  isEditing.value = true;
  currentEditingSurcharge.value = surcharge;
  showCreateEditDialog.value = true;

  // Count all surcharges in the same group using the Map
  const groupSurcharges = loadedGroupSurcharges.value.get(surcharge.uuid) || [];
  groupCount.value = groupSurcharges.length;
}

function addToGroupSurcharge(uuid: string): void {
  isEditing.value = false;
  currentEditingSurcharge.value = null;
  showCreateEditDialog.value = true;

  addToGroupUuId.value = uuid;
}

/**
 * Opens the bulk edit dialog for selected surcharges
 * Initializes bulk edit data with common values from selected surcharges
 */
function openBulkEditDialog(): void {
  const selectedIds = selectedIndividualSurcharges.value;
  if (selectedIds.length === 0) {
    return;
  }
  showBulkEditDialog.value = true;
}

/**
 * Determines the surcharge level for a group (division, client, or fleet)
 * @param group - The grouped fuel surcharge
 * @returns String indicating the surcharge level
 */
function formatGroupSurchargeLevel(group: GroupedFuelSurcharge): string {
  // Get the first surcharge from the group to determine level
  const firstSurcharge = allFuelSurchargeRates.value.find(
    (s) => s.uuid === group.uuid,
  );
  if (!firstSurcharge) {
    return 'Unknown';
  }
  // Check if it's a client surcharge
  if (
    firstSurcharge instanceof ClientFuelSurchargeRate &&
    firstSurcharge.clientIds
  ) {
    const clientIds = firstSurcharge.clientIds;

    // Check for division level indicators
    if (clientIds.length === 1 && ['0', 'CS'].includes(String(clientIds[0]))) {
      return clientIds[0] === 'CS' ? 'CASH SALES' : 'DIVISION';
    } else if (clientIds.length > 0) {
      return 'CLIENT';
    }
  }
  // Check if it's a fleet asset surcharge
  if (
    firstSurcharge instanceof FleetAssetFuelSurchargeRate &&
    firstSurcharge.fleetAssetIds
  ) {
    const fleetAssetIds = firstSurcharge.fleetAssetIds;

    // Check for division level indicator
    if (fleetAssetIds.length === 1 && String(fleetAssetIds[0]) === '0') {
      return 'DIVISION';
    } else if (fleetAssetIds.length > 0) {
      return 'FLEET ASSET';
    }
  }
  return 'DIVISION';
}

// Function called from template when status filter changes
async function loadRateVariations(): Promise<void> {
  // Clear loaded cache when status changes
  loadedGroupSurcharges.value.clear();
  loadingGroups.value = [];
  selectedIndividualSurcharges.value = [];
  expandedGroupsForSearch.value = [];
  allFuelSurchargeRates.value = [];

  await fetchFuelSurchargeData();

  // Emit refresh event to parent component
  emit('refreshServiceRateList');
}

async function forceRefreshGroups(uuid: string) {
  loadedGroupSurcharges.value.delete(uuid);
  await loadGroupSurcharges(uuid);

  // Emit refresh event to parent component
  emit('refreshServiceRateList');
}

// Lazy loading functions
async function loadGroupSurcharges(uuid: string): Promise<void> {
  // Only proceed if lazy loading is enabled
  if (!shouldUseLazyLoading.value) {
    return;
  }

  // Check if already loaded or loading
  if (
    loadedGroupSurcharges.value.has(uuid) ||
    loadingGroups.value.includes(uuid)
  ) {
    return;
  }

  // Add to loading state
  loadingGroups.value.push(uuid);

  try {
    let surcharges: FuelSurchargeRate[] | null = null;

    // Determine which API to call based on component type
    if (
      props.componentType === FuelComponentType.CLIENT ||
      props.componentType === FuelComponentType.CASH_SALE
    ) {
      surcharges =
        await fuelLevyStore.getAllClientFuelSurchargeRatesByUUID(uuid);
    } else if (props.componentType === FuelComponentType.FLEET_ASSET) {
      surcharges =
        await fuelLevyStore.getAllFleetAssetFuelSurchargeRatesByUUID(uuid);
    }

    if (surcharges) {
      // Cache the loaded surcharges with selection properties
      loadedGroupSurcharges.value.set(
        uuid,
        surcharges as FuelSurchargeRateSelection[],
      );
    } else {
      // Set empty array if no data found
      loadedGroupSurcharges.value.set(uuid, []);
    }
  } catch (error) {
    console.error('Error loading group surcharges:', error);
    showNotification('Failed to load individual surcharges', {
      type: HealthLevel.ERROR,
    });
    // Set empty array on error
    loadedGroupSurcharges.value.set(uuid, []);
  } finally {
    // Remove from loading state
    const index = loadingGroups.value.indexOf(uuid);
    if (index > -1) {
      loadingGroups.value.splice(index, 1);
    }
  }
}

/**
 * Toggles the expansion state of a group in the data table
 * @param props - Table row props containing the item data and expansion state
 */
async function toggleGroupExpansion(props: {
  item: GroupedFuelSurcharge;
  expanded: boolean;
}): Promise<void> {
  const uuid = props.item.uuid;

  if (props.expanded) {
    // Collapsing the group
    props.expanded = false;
    // Remove from search expansion tracking
    const index = expandedGroupsForSearch.value.indexOf(uuid);
    if (index > -1) {
      expandedGroupsForSearch.value.splice(index, 1);
    }

    // Clear any selected checkboxes for this group when collapsing
    clearAllSurchargesSelection();
  } else {
    // Expanding the group - expand immediately and show loading if needed
    props.expanded = true;
    // Add to search expansion tracking
    if (!expandedGroupsForSearch.value.includes(uuid)) {
      expandedGroupsForSearch.value.push(uuid);
    }

    // Clear any previously selected checkboxes from other groups when expanding a new group
    clearAllSurchargesSelection(uuid);

    // Only trigger lazy loading if it's enabled and data isn't already loaded
    if (shouldUseLazyLoading.value && !loadedGroupSurcharges.value.has(uuid)) {
      // Start loading asynchronously (don't await to allow immediate expansion)
      loadGroupSurcharges(uuid).catch((error) => {
        console.error('Error loading group surcharges:', error);
      });
    }
  }
}

/**
 * Fetches fuel surcharge data based on component type and entity ID
 * Handles different component types (DIVISION, CLIENT, FLEET_ASSET, etc.)
 */
async function fetchFuelSurchargeData(): Promise<void> {
  try {
    isLoading.value = true;
    let data: FuelSurchargeRate[] | null = null;

    switch (props.componentType) {
      case FuelComponentType.CASH_SALE:
        data = await fuelLevyStore.getClientFuelSurchargeList('CS');
        break;

      case FuelComponentType.CLIENT:
        if (props.entityId) {
          data = await fuelLevyStore.getClientFuelSurchargeList(props.entityId);
        } else {
          data = await fuelLevyStore.getAllClientDivisionFuelSurcharges(
            selectedStatus.value,
          );
        }

        // Filter out Cash Sales surcharges for CLIENT component type
        if (data) {
          data = data.filter((surcharge) => {
            // Check if it's a client surcharge with clientIds property
            if (
              surcharge instanceof ClientFuelSurchargeRate &&
              surcharge.clientIds
            ) {
              // Exclude surcharges that contain 'CS' in clientIds (Cash Sales)
              const isCashSale = surcharge.clientIds.includes('CS');
              return !isCashSale;
            }
            return true; // Keep non-client surcharges
          });
        }
        break;

      case FuelComponentType.FLEET_ASSET:
        if (props.entityId) {
          data = await fuelLevyStore.getAllFleetAssetFuelSurchargeRates(
            props.entityId,
          );
        } else {
          data = await fuelLevyStore.getAllDivisionFleetAssetFuelSurchargeRates(
            selectedStatus.value,
          );
        }
        break;

      default:
        console.warn('Unsupported component type:', props.componentType);
        break;
    }

    if (data) {
      allFuelSurchargeRates.value = data;
    }
  } catch (error) {
    console.error('Error fetching fuel surcharge data:', error);
    showNotification('Failed to load fuel surcharge data', {
      type: HealthLevel.ERROR,
    });
  } finally {
    isLoading.value = false;
  }
}

/**
 * Handles the bulk edit dialog close event
 * Resets all checkbox selections when the dialog is closed
 */
function handleBulkEditDialogClose(isVisible: boolean): void {
  showBulkEditDialog.value = isVisible;

  // If dialog is being closed (isVisible = false), reset all selections
  if (!isVisible) {
    clearAllSurchargesSelection();
  }
}

onMounted(() => {
  fetchFuelSurchargeData();
});
</script>

<style scoped lang="scss">
.fuel-surcharge-levy-details {
  .fuel-surcharge-table-container {
    // Expanded content styling
    .expanded-content {
      background-color: var(--background-color-300);
      border-bottom: 2px solid black;
    }

    // Expansion icon animation
    .expansion-icon {
      padding: 4px;
      transition: transform 0.2s ease;
      color: $warning;
      font-weight: 500;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }
}
.no-limit-text {
  display: flex;
  font-size: $font-size-14;
  color: $light-text-color;
  margin-left: 24px;
  margin-bottom: 24px;
}

.inner-table-name {
  width: 240px;
}

.inner-table-rate {
  width: 180px;
}

.inner-table-valid-from {
  width: 190px;
}

.inner-table-valid-to {
  width: 190px;
}

// .inner-table-status {
//   width: 140px;
// }

// .inner-table-actions {
//   width: 190px;
// }

.date-adjustment-message {
  em {
    color: #666;
    font-style: italic;
  }
}

.success {
  background-color: transparent !important;
  color: $success;
}

.default {
  color: grey;
}
</style>
