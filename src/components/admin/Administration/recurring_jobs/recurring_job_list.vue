<template>
  <div class="recurring-job-list">
    <TableTitleHeader>
      <template #title>
        <GTitle
          title="Permanent Jobs"
          :subtitle="`${
            allTemplates.filter((t) => !t.isCancelled).length
          } Active - (${allTemplates.length} total)`"
          :divider="false"
        />
      </template>

      <template #buttons>
        <v-layout align-center class="banner-custom" px-2>
          <span>
            <v-checkbox
              color="light-blue"
              label="Filter Direct to Invoice Jobs"
              v-model="showDirectToInvoice"
              class="ma-1 pr-2"
              hide-details
            />
          </span>
          <span>
            <v-checkbox
              color="light-blue"
              label="Include Cancelled Jobs"
              v-model="showCancelled"
              class="ma-1"
              hide-details
              :disabled="showDirectToInvoice"
            />
          </span>
        </v-layout>
      </template>
    </TableTitleHeader>
    <v-data-table
      class="accounting-data-table mt-2"
      :headers="tableHeaders"
      :items="recurringJobTemplateList"
      hide-actions
      :loading="isAwaitingResponse"
    >
      <template v-slot:items="props">
        <tr>
          <td>
            {{ props.item.client.clientName }}
          </td>
          <td>
            {{ props.item.initialJobId }}
          </td>
          <td>
            {{ props.item.stopsSummary }}
          </td>
          <td>
            {{
              returnServiceTypeLongNameFromId(props.item.serviceTypeId ?? -1)
            }}
          </td>
          <td>
            {{
              props.item.isClientTripRate
                ? `Quoted Rate: $${DisplayCurrencyValue(
                    props.item.clientTripRateValue,
                  )}`
                : returnRateTypeLongNameFromId(
                    props.item.serviceTypeObject.rateTypeId ?? -1,
                  )
            }}
          </td>
          <td>
            {{
              props.item.isDriverTripRate
                ? `Quoted Rate: $${DisplayCurrencyValue(
                    props.item.driverTripRateValue,
                  )}`
                : returnRateTypeLongNameFromId(
                    props.item.serviceTypeObject.rateTypeId !== JobRateType.TRIP
                      ? props.item.serviceTypeObject.rateTypeId
                      : JobRateType.TIME,
                  )
            }}
          </td>
          <td>
            {{ props.item.directToInvoicing ? 'Yes' : 'No' }}
          </td>

          <td>
            {{ props.item.csrAssignedId }}
          </td>
          <td>
            {{ props.item.driverName }}
          </td>
          <td v-if="props.item.recurrenceDetails.recurrenceHistory.length > 0">
            {{ props.item.recurrenceDetails.recurrenceHistory.length }} Jobs
          </td>
          <td v-else>No Jobs</td>
          <td>
            {{
              props.item.recurrenceDetails.nextScheduledRecurrence
                ? returnFormattedDate(
                    props.item.recurrenceDetails.nextScheduledRecurrence,
                    `ddd DD/MM/YY`,
                  )
                : '-'
            }}
          </td>
          <td
            v-if="props.item.recurrenceDetails.nextScheduledRecurrence === 0"
            class="text-xs-right"
          >
            Cancelled
          </td>
          <td v-else class="text-xs-right">
            <v-menu left>
              <template v-slot:activator="{ on }">
                <v-icon size="16" class="action-icon" v-on="on"
                  >fas fa-ellipsis-v
                </v-icon>
              </template>
              <v-list class="v-list-custom" dense>
                <v-list-tile
                  @click="editSelectedRecurringJobTemplate(props.item)"
                  :disabled="!isAuthorised()"
                >
                  <v-list-tile-title class="pr-2"
                    >Edit Permanent Job</v-list-tile-title
                  >
                </v-list-tile>
                <v-list-tile
                  @click="prepareForTemplateCancellation(props.item)"
                  :disabled="!isAuthorised()"
                >
                  <v-list-tile-title class="pr-2"
                    >Cancel Permanent Job</v-list-tile-title
                  >
                </v-list-tile>
              </v-list>
            </v-menu>
          </td>
        </tr>
      </template>
    </v-data-table>

    <!-- EDIT JOB DIALOG -->
    <EditRecurringJobDialog
      :showEditingRecurringJobDialog.sync="showEditingRecurringJobDialog"
      :currentRecurringJob="currentEditedRecurringJob"
      :allTemplates="allTemplates"
    />
    <!-- CANCEL JOB DIALOG -->
    <v-dialog
      v-model="viewingCancellationDialog"
      width="400px"
      persistent
      content-class="v-dialog-custom"
    >
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Cancel Recurring Job?</span>

        <div
          class="app-theme__center-content--closebutton"
          @click="viewingCancellationDialog = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>

      <v-layout
        class="app-theme__center-content--body"
        v-if="currentPendingCancellationRecurringJob"
      >
        <v-flex md12>
          <v-layout row wrap class="side-column" pa-3>
            <v-flex md12 py-1 px-3 mb-3>
              <v-alert type="error" class="ma-0" :value="true"
                >This action is cannot be undone.
              </v-alert>
            </v-flex>
            <v-flex md6>
              <v-layout class="side-column__label" justify-end>
                Customer:
              </v-layout>
              <v-layout class="side-column__label" justify-end>
                Recurring Job ID:
              </v-layout>
            </v-flex>
            <v-flex md6>
              <v-layout class="side-column__value">
                {{
                  currentPendingCancellationRecurringJob.client?.clientName ??
                  'Unknown'
                }}
              </v-layout>
              <v-layout class="side-column__value">
                {{ currentPendingCancellationRecurringJob.initialJobId }}
              </v-layout>
            </v-flex>
          </v-layout>
          <v-layout row wrap>
            <v-flex md12>
              <v-divider></v-divider>
              <v-layout justify-space-between>
                <v-btn
                  flat
                  color="red"
                  @click="viewingCancellationDialog = false"
                  >Back</v-btn
                >
                <v-btn
                  depressed
                  color="blue"
                  @click="sendTemplateCancellationRequest()"
                  >Confirm Cancellation</v-btn
                >
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-dialog>
  </div>
</template>
<script setup lang="ts">
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  GENERIC_SUCCESS_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  returnRateTypeLongNameFromId,
  returnServiceTypeLongNameFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { hasAdminOrCsrOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import RecurringJobTemplate from '@/interface-models/Jobs/RecurringJob/RecurringJobTemplate';
import { useRecurringJobStore } from '@/store/modules/RecurringJobStore';
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  onMounted,
  ref,
} from 'vue';
import EditRecurringJobDialog from './recurring_job_edit_dialog.vue';

const recurringJobStore = useRecurringJobStore();

const showCancellationConfirmationDialog: Ref<boolean> = ref(false);
const showEditingRecurringJobDialog: Ref<boolean> = ref(false);
const currentPendingCancellationRecurringJob: Ref<RecurringJobTemplate | null> =
  ref(null);
const currentEditedRecurringJob: Ref<RecurringJobTemplate | null> = ref(null);

const showCancelled: Ref<boolean> = ref(false);
const showDirectToInvoice: Ref<boolean> = ref(false);
const isAwaitingResponse: Ref<boolean> = ref(false);

const allTemplates: Ref<RecurringJobTemplate[]> = ref([]);

/**
 * Returns the data to be displayed in the table. If `showCancelled` is true,
 * returns all templates. Otherwise, returns only active templates.
 */
const recurringJobTemplateList: ComputedRef<RecurringJobTemplate[]> = computed(
  () => {
    if (isAwaitingResponse.value) {
      return [];
    }

    return allTemplates.value.filter((t) => {
      if (showDirectToInvoice.value) {
        return t.directToInvoicing === true;
      }
      return showCancelled.value || !t.isCancelled;
    });
  },
);

/**
 * Controls visibility of cancellation dialog. When closing the dialog, also clears the current pending cancellation recurring job.
 */
const viewingCancellationDialog: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return showCancellationConfirmationDialog.value;
  },
  set(newValue: boolean): void {
    showCancellationConfirmationDialog.value = newValue;
    if (!newValue) {
      currentPendingCancellationRecurringJob.value = null;
    }
  },
});

/**
 * Headers applied to the data table
 */
const tableHeaders: TableHeader[] = [
  {
    text: 'Client',
    value: 'client.clientName',
    align: 'left',
    sortable: true,
  },
  { text: 'Job ID', value: 'initialJobId', align: 'left', sortable: true },
  {
    text: 'Stops',
    value: 'pudItems.length',
    align: 'left',
    sortable: false,
  },
  {
    text: 'Job Service',
    value: 'serviceTypeId',
    align: 'left',
    sortable: false,
  },
  {
    text: 'Client Rate Type',
    value: 'serviceTypeId',
    align: 'left',
    sortable: false,
  },
  {
    text: 'Fleet Rate Type',
    value: 'isDriverTripRate',
    align: 'left',
    sortable: false,
  },
  {
    text: 'Direct to Invoicing',
    value: 'directToInvoicing',
    align: 'left',
    sortable: false,
  },
  {
    text: 'Allocated Fleet',
    value: 'csrAssignedId',
    align: 'left',
    sortable: false,
  },
  { text: 'Driver', value: 'driverName', align: 'left', sortable: false },
  {
    text: 'Jobs Created',
    value: 'initialJobId',
    align: 'left',
    sortable: false,
  },
  {
    text: 'Next Scheduled',
    value: 'recurrenceDetails.nextScheduledRecurrence',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Action',
    value: 'recurrenceDetails.nextScheduledRecurrence',
    align: 'right',
    sortable: false,
  },
];

// Set currently editing jobRun to selected and show dialog
function prepareForTemplateCancellation(template: RecurringJobTemplate) {
  viewingCancellationDialog.value = true;
  currentPendingCancellationRecurringJob.value = Object.assign(
    new RecurringJobTemplate(),
    template,
  );
}

// Send recurring job cancellation request
async function sendTemplateCancellationRequest() {
  if (currentPendingCancellationRecurringJob.value === null) {
    return;
  }
  currentPendingCancellationRecurringJob.value.recurrenceDetails.nextScheduledRecurrence = 0;
  // Send request to cancel job and show notification
  await sendUpdatedRecurringJobTemplate(
    currentPendingCancellationRecurringJob.value,
  );
  viewingCancellationDialog.value = false;
}

// Set currently editing jobRun to selected and show dialog
function editSelectedRecurringJobTemplate(template: RecurringJobTemplate) {
  // await getClientDetails(template.client?.id);
  currentEditedRecurringJob.value = Object.assign(
    new RecurringJobTemplate(),
    template,
  );
  showEditingRecurringJobDialog.value = true;
}

/**
 * Sends an updated recurring job template to the server to be saved. Shows a notification depending on whether the template was saved successfully or not.
 *
 * @param {RecurringJobTemplate} template - The updated recurring job template to send.
 * @returns {Promise<void>} - A promise that resolves when the template is successfully sent.
 */
async function sendUpdatedRecurringJobTemplate(
  template: RecurringJobTemplate,
): Promise<void> {
  const updatedTemplate =
    await recurringJobStore.saveRecurringJobTemplate(template);

  // Show notification based on response
  if (updatedTemplate === null) {
    showNotification(
      'Error occurred when updating Permanent Job. Please try again',
    );
  } else {
    // Replace the template in the list with the updated one
    const index = allTemplates.value.findIndex(
      (t) => updatedTemplate._id && t._id === updatedTemplate._id,
    );
    if (index !== -1) {
      allTemplates.value.splice(
        index,
        1,
        Object.assign(new RecurringJobTemplate(), updatedTemplate),
      );
    }
    showNotification(GENERIC_SUCCESS_MESSAGE, { type: HealthLevel.SUCCESS });
  }
}

function isAuthorised(): boolean {
  return hasAdminOrCsrOrTeamLeaderOrBranchManagerRole();
}

// Handles response for recurring job list request. Initialise each item and
// then set to all templates list.
function handleRecurringJobListResponse(
  templateList: RecurringJobTemplate[],
): void {
  allTemplates.value = templateList.map((template) =>
    Object.assign(new RecurringJobTemplate(), template),
  );
}

/**
 * Fetches recurring job templates and handles the response.
 *
 * This method sets `isAwaitingResponse` to true, sends a request to fetch
 * recurring job templates, handles the response by calling
 * `handleRecurringJobListResponse`, and then sets `isAwaitingResponse` to
 * false. If the response is null, an empty array is passed to
 * `handleRecurringJobListResponse`.

  * @returns {Promise<void>} A Promise that resolves when the method has
  * completed.
  */
async function getRecurringJobTemplates(): Promise<void> {
  isAwaitingResponse.value = true;
  const response = await recurringJobStore.getRecurringJobTemplates();
  handleRecurringJobListResponse(response ?? []);
  isAwaitingResponse.value = false;
}

// Request recurring job list on mount
onMounted(async () => {
  getRecurringJobTemplates();
});
</script>

<style scoped lang="scss">
.side-column {
  .side-column__label {
    font-size: $font-size-12;
    text-transform: uppercase;
    font-weight: 600;
    color: rgb(186, 188, 209) !important;
    padding-right: 12px;
    padding-top: 3px;
    letter-spacing: 0.02em;
  }

  .side-column__value {
    font-size: $font-size-12;
    font-weight: 400;
    padding-top: 3px;
  }
}

.action-icon {
  color: var(--text-color) !important;
}
</style>
