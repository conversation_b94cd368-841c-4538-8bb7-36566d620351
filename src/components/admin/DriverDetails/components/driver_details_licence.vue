<template>
  <div class="driver-details-licence-container">
    <v-data-table
      class="gd-dark-theme"
      :headers="headers"
      :items="licenceDetailsTableData"
      :rows-per-page-items="[10, 20]"
    >
      <template v-slot:items="props">
        <tr
          @click="editLicenceDetails(props.item.licenceTypeId)"
          style="cursor: pointer"
        >
          <td>{{ props.item.licenceName }}</td>
          <td>{{ props.item.status }}</td>
          <td>{{ props.item.licenceNumber }}</td>
          <td>{{ props.item.licenceClassNames }}</td>
          <td>{{ props.item.licenceTypeNames }}</td>
          <td>{{ props.item.issueDate }}</td>
          <td>{{ props.item.expiry }}</td>
        </tr>
      </template>
    </v-data-table>

    <ContentDialog
      v-if="licenceDetails"
      :showDialog.sync="licenceDetailsDialogIsOpen"
      :title="licenceTypeName"
      :isConfirmUnsaved="isConfirmUnsaved"
      :width="'800px'"
      :confirmBtnText="'save'"
      :isDisabled="!isAuthorised()"
      :isLoading="isLoading"
      contentPadding="pa-0"
      @confirm="saveLicence"
      @cancel="cancelLicenceDetails"
    >
      <v-form ref="licenceForm">
        <v-layout class="body-scrollable--75 body-min-height--65 pa-3" row wrap>
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">1. Key Details</h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    {{
                      licenceDetails.licenseType === 4
                        ? 'Issuer Name'
                        : `Licence
                  ID Number`
                    }}
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8 v-if="licenceDetails.licenseType === 4">
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  :disabled="!isAuthorised()"
                  label="Issued By"
                  v-model.trim="licenceDetails.issuedBy"
                  color="light-blue"
                  :rules="[rules.required]"
                  autofocus
                ></v-text-field>
              </v-flex>
              <v-flex md8 v-else>
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  :disabled="!isAuthorised()"
                  label="Licence ID Number"
                  v-model.trim="licenceDetails.licenseId"
                  color="light-blue"
                  :rules="[rules.required]"
                  autofocus
                ></v-text-field>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex
            md12
            v-if="
              licenceDetails.licenseType !== 3 &&
              licenceDetails.licenseType !== 4
            "
          >
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Class
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-select
                  label="Class"
                  v-model="licenceDetails.licenseClass"
                  :items="licenceClassesList"
                  item-text="selectName"
                  multiple
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  :disabled="!isAuthorised()"
                  :rules="[rules.listRequired]"
                  item-value="id"
                ></v-select>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md12 v-if="licenceDetails.licenseType === 1">
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Type
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-select
                  label="Type"
                  v-model="licenceDetails.driverLicenseType[0]"
                  :rules="[rules.required]"
                  :items="driverLicenseTypes"
                  item-text="longName"
                  item-value="id"
                  :disabled="!isAuthorised()"
                  class="v-solo-custom"
                  solo
                  flat
                ></v-select>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6 class="subheader--faded pr-3 pb-0">Issue Date</h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <DateTimeInputs
                  :epochTime.sync="licenceDetails.issueDate"
                  :enableValidation="true"
                  :type="DateTimeType.DATE_START_OF_DAY"
                  dateLabel="Issue Date"
                  :soloInput="true"
                  :boxInput="false"
                  :readOnly="!isAuthorised()"
                  :hintTextType="HintTextType.FORMATTED_SELECTION"
                ></DateTimeInputs>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12 v-if="licenceDetails.licenseType !== 4">
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Expiry
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <DateTimeInputs
                  :epochTime.sync="licenceDetails.expiry"
                  :enableValidation="true"
                  :type="DateTimeType.DATE_END_OF_DAY"
                  dateLabel="Expiry"
                  :soloInput="true"
                  :boxInput="false"
                  :readOnly="!isAuthorised()"
                  :isRequired="true"
                  :hintTextType="HintTextType.FORMATTED_SELECTION"
                ></DateTimeInputs>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12>
            <v-layout>
              <v-flex md4>
                <v-layout align-center class="form-field-label-container">
                  <h6
                    class="subheader--faded pr-3 pb-0 form-field-required-marker"
                  >
                    Active:
                  </h6>
                </v-layout>
              </v-flex>
              <v-flex md8>
                <v-checkbox
                  v-model="licenceDetails.isActive"
                  persistent-hint
                  :disabled="!isAuthorised()"
                  :hint="
                    licenceDetails.isActive
                      ? 'This Induction will appear in Compliance requirements'
                      : 'This Induction will not appear in Compliance requirements'
                  "
                  color="light-blue"
                  class="mt-2"
                />
              </v-flex>
            </v-layout>
          </v-flex>
          <v-flex md12 pb-3
            ><v-layout align-center>
              <h5 class="subheader--bold pr-3 pt-1">2. Add Licence Images</h5>
              <v-flex>
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
          </v-flex>

          <v-flex md6 class="pr">
            <file-upload
              :imageLabel="'FRONT SIDE'"
              :attachmentSingle="false"
              :formDisabled="!isAuthorised()"
              :documentTypeId="AttachmentTypes.LICENSE_FRONT"
              :attachmentArray="licenceDetails.photos"
              :isLoading.sync="isLoading"
            >
            </file-upload>
          </v-flex>
          <v-flex md6 class="pl">
            <file-upload
              :imageLabel="'BACK SIDE'"
              :attachmentSingle="false"
              :documentTypeId="AttachmentTypes.LICENSE_REAR"
              :attachmentArray="licenceDetails.photos"
              :formDisabled="!isAuthorised()"
              :isLoading.sync="isLoading"
            >
            </file-upload>
          </v-flex>
        </v-layout>
      </v-form>
    </ContentDialog>
  </div>
</template>

<script setup lang="ts">
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import LicenseDetails from '@/interface-models/Driver/DriverDetails/AdditionalObjects/LicenseDetails';
import {
  DriverDetails,
  SaveDriverDetailsResponse,
} from '@/interface-models/Driver/DriverDetails/DriverDetails';
import Attachment from '@/interface-models/Generic/Attachment/Attachment';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import driverLicenseClasses, {
  DriverLicenseClasses,
} from '@/interface-models/Generic/DriverLicenseClasses/DriverLicenseClasses';
import driverLicenseTypes, {
  DriverLicenseTypes,
} from '@/interface-models/Generic/DriverLicenseTypes/DriverLicenseTypes';
import highRiskLicenseClasses, {
  LicenseClassesInterface,
} from '@/interface-models/Generic/HighRiskLicenseClasses/HighRiskLicenseClasses';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { ComputedRef, Ref, computed, onMounted, ref } from 'vue';

import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import FileUpload from '@/components/common/file-upload/file_upload.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import licenseType, {
  LicenseType,
} from '@/interface-models/Generic/LicenseType/LicenseType';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { Validation } from '@/interface-models/Generic/Validation';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import Mitt from '@/utils/mitt';
import moment from 'moment-timezone';

interface IProps {
  licenceDetailsList: LicenseDetails[];
  isEdited: boolean;
  driverDetails?: DriverDetails;
}

const props = withDefaults(defineProps<IProps>(), {
  licenceDetailsList: () => [],
  isEdited: false,
  driverDetails: undefined,
});

const licenceForm: Ref<any> = ref(null);

// const attachmentTypes = ref(AttachmentTypes);
const licenceDetails: Ref<LicenseDetails | null> = ref(null);
const licenceDetailsDialogIsOpen: Ref<boolean> = ref(false);
const awaitingDriverSaveResponse: Ref<boolean> = ref(false);
const isLoading: Ref<boolean> = ref(false);
const unEditedLicenseDetails: Ref<LicenseDetails | null> = ref(null);

interface LicenceDetailsTableData {
  licenceNumber: string;
  status: string;
  licenceClassNames: string;
  licenceName: string;
  issueDate: string;
  expiry: string;
  attachmentIds: string[];
  licenceTypeNames: string;
  licenceTypeId: number;
}
const licenceDetailsTableData: ComputedRef<LicenceDetailsTableData[]> =
  computed(() => {
    const tableData: LicenceDetailsTableData[] = licenseType.map(
      (licenceType: LicenseType) => {
        const licenceTypeId: number = licenceType.id;
        const licenceName = licenceType.longName;
        const existingLicence = props.licenceDetailsList.find(
          (x: LicenseDetails) => x.licenseType === licenceType.id,
        );
        // Initialize status with 'No Licence Added'
        let status = 'Not Added';

        // Check if there is an existing licence
        if (existingLicence) {
          // If the licence is not active, set status to 'Inactive'
          if (!existingLicence.isActive) {
            status = 'Inactive';
          } else if (licenceType.id !== 4 && existingLicence.expiry) {
            const companyDetailsStore = useCompanyDetailsStore();
            // If the licence type is not VOC (4), check the expiry status
            const expiryDate = moment.tz(
              existingLicence.expiry,
              companyDetailsStore.userLocale,
            );
            const now = moment.tz(companyDetailsStore.userLocale);
            // Calculate the date and time 30 days from now
            const thirtyDaysFromNow = now.clone().add(30, 'days');

            // If the expiry date is in the past, set status to 'Expired'
            if (expiryDate.isBefore(now)) {
              status = 'Expired';
            } else if (expiryDate.isBefore(thirtyDaysFromNow)) {
              // If the expiry date is within the next 30 days, calculate the
              // number of days until expiry and set status to 'Expiring in X
              // days'
              const daysUntilExpiry = expiryDate.diff(now, 'days');
              status = `Expiring in ${daysUntilExpiry} days`;
            } else {
              // If the expiry date is more than 30 days in the future, set status to 'Active'
              status = 'Active';
            }
          } else {
            // If the licence type is 4 or there is no expiry date, set status to 'Active'
            status = 'Active';
          }
        }

        const licenceNumber: string = existingLicence
          ? existingLicence.licenseId
            ? existingLicence.licenseId
            : existingLicence.issuedBy
              ? existingLicence.issuedBy
              : '-'
          : '-';
        const attachmentIds: string[] = existingLicence
          ? existingLicence.photos.map((photo: Attachment) => photo.id)
          : [];

        let licenceClassNames = '';
        const classNames: string[] = [];
        let licenceTypeNames = '';
        const typeNames: string[] = [];

        let classTypeList: DriverLicenseClasses[] | LicenseClassesInterface[] =
          [];

        if (licenceType.id === 1) {
          classTypeList = driverLicenseClasses;
        } else if (licenceType.id === 2) {
          classTypeList = highRiskLicenseClasses;
        }

        if (existingLicence) {
          for (const classId of existingLicence.licenseClass) {
            const foundLicenceClass = classTypeList.find(
              (x: DriverLicenseClasses) => x.id === classId,
            );
            if (foundLicenceClass) {
              classNames.push(foundLicenceClass.longName);
            }
          }

          for (const typeId of existingLicence.driverLicenseType) {
            const foundLicenceType = driverLicenseTypes.find(
              (x: DriverLicenseTypes) => x.id === typeId,
            );
            if (foundLicenceType) {
              typeNames.push(foundLicenceType.longName);
            }
          }
        }

        licenceClassNames = classNames.length > 0 ? classNames.join(', ') : '-';
        licenceTypeNames = typeNames.length > 0 ? typeNames.join(', ') : '-';

        const issueDate =
          existingLicence && existingLicence.issueDate
            ? returnFormattedDate(existingLicence.issueDate)
            : '-';
        const expiry =
          existingLicence && existingLicence.expiry
            ? returnFormattedDate(existingLicence.expiry)
            : '-';

        return {
          licenceTypeId,
          status,
          licenceName,
          licenceNumber,
          licenceClassNames,
          issueDate,
          expiry,
          attachmentIds,
          licenceTypeNames,
        };
      },
    );
    return tableData;
  });

const headers: TableHeader[] = [
  {
    text: 'Licence',
    align: 'left',
    value: 'InsurerName',
    sortable: false,
  },
  {
    text: 'Status',
    align: 'left',
    value: 'status',
    sortable: false,
  },
  {
    text: 'Licence # / Issuer',
    align: 'left',
    sortable: false,
    value: 'licenceNumber',
  },
  {
    text: 'Class',
    align: 'left',
    value: 'licenceClassNames',
    sortable: false,
  },
  {
    text: 'Type',
    align: 'left',
    value: 'licenceTypeNames',
    sortable: false,
  },
  {
    text: 'Issue Date',
    align: 'left',
    value: 'issueDate',
    sortable: false,
  },
  {
    text: 'Expiry',
    align: 'left',
    value: 'expiry',
    sortable: false,
  },
];
/**
 * Computes the validation rules from the store.
 * @returns {ComputedRef<Validation>} A computed reference to the validation rules.
 */
const rules: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

const licenceTypeName: ComputedRef<string> = computed(() => {
  const licenceType = licenseType.find(
    (x: LicenseType) => x.id === licenceDetails.value?.licenseType,
  );
  return licenceType?.longName ?? '';
});

function editLicenceDetails(licenceTypeId: number) {
  const licence = props.licenceDetailsList.find(
    (x: LicenseDetails) => x.licenseType === licenceTypeId,
  );
  if (licence) {
    licenceDetails.value = { ...licence };
    unEditedLicenseDetails.value = { ...licence };
  } else {
    licenceDetails.value = new LicenseDetails();
    licenceDetails.value.licenseType = licenceTypeId;
    unEditedLicenseDetails.value = new LicenseDetails();
    unEditedLicenseDetails.value.licenseType = licenceTypeId;
  }
  licenceDetailsDialogIsOpen.value = true;
}

function cancelLicenceDetails() {
  licenceDetailsDialogIsOpen.value = false;
  licenceDetails.value = null;
  unEditedLicenseDetails.value = null;
}

const licenceClassesList: ComputedRef<
  DriverLicenseClasses[] | LicenseClassesInterface[]
> = computed(() => {
  const incomingId = licenceDetails.value?.licenseType;

  let data: DriverLicenseClasses[] | LicenseClassesInterface[] = [];
  if (incomingId === 1) {
    data = driverLicenseClasses;
  } else if (incomingId === 2) {
    data = highRiskLicenseClasses;
  } else {
    data = [];
  }

  return data.map((c) => {
    return { ...c, selectName: `${c.shortName} - ${c.longName}` };
  });
});

/**
 * Saves the driver's licence details by performing a full DriverDetails
 * document save. If no driver is selected or the licence details are invalid,
 * a notification is shown. If the licence is a VOC, the expiry date is also unset.
 */
function saveLicence() {
  if (!props.driverDetails) {
    showNotification('Please select a driver before adding a licence');
    return;
  }
  if (!licenceDetails.value || !licenceForm.value?.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }
  if (licenceDetails.value.licenseType === 4) {
    delete licenceDetails.value.expiry;
  }
  const existingLicenceIndex: number = props.licenceDetailsList.findIndex(
    (x: LicenseDetails) => x.licenseType === licenceDetails.value!.licenseType,
  );
  if (existingLicenceIndex > -1) {
    props.licenceDetailsList.splice(
      existingLicenceIndex,
      1,
      licenceDetails.value,
    );
  } else {
    props.licenceDetailsList.push(licenceDetails.value);
  }
  awaitingDriverSaveResponse.value = true;
  props.driverDetails.save(null);
}

function setSavedDriverDetails(driverDetails: DriverDetails | null) {
  if (
    !!props.driverDetails?.driverId &&
    driverDetails?.driverId === props.driverDetails.driverId
  ) {
    showNotification('Licence successfully saved.', {
      type: HealthLevel.SUCCESS,
      title: 'Driver Details - Licences',
    });
    cancelLicenceDetails();
  }
}

const isConfirmUnsaved: Ref<boolean> = computed(() => {
  if (!licenceDetails.value || !unEditedLicenseDetails.value) {
    return false;
  }
  return (
    JSON.stringify(licenceDetails.value) !==
    JSON.stringify(unEditedLicenseDetails.value)
  );
});

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

/**
 * Configures a listener to react to saved driver details events based on the provided `listen` flag. When enabled,
 * it listens for 'savedNewDriver' events and updates the local state with the received driver details.
 * Please note that we don't clear the listener in this component. We do so in the main driver_details_index on destroy.
 * Possible better solution here is to emit the save request back so it is all handled in driver_details_index; that way we
 * wouldnt require listeners in every driver sub component.
 *
 * @param {boolean} listen - Determines whether to start or stop listening for driver update events.
 * @returns {void}
 */
function listenForDriverUpdates(listen: boolean): void {
  if (!listen) {
    Mitt.off('savedNewDriver');
    return;
  }
  Mitt.on('savedNewDriver', (savedDriver: SaveDriverDetailsResponse) => {
    if (awaitingDriverSaveResponse.value) {
      setSavedDriverDetails(savedDriver?.driverDetails?.driver ?? null);
      awaitingDriverSaveResponse.value = false;
    }
  });
}
onMounted(() => {
  listenForDriverUpdates(true);
});
</script>
