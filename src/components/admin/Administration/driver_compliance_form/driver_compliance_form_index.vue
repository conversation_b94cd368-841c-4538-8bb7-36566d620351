<template>
  <div>
    <TableTitleHeader>
      <template #title>
        <GTitle
          title="Driver Compliance Forms"
          subtitle="Edit & Update Driver Compliance Forms"
          :divider="false"
        />
      </template>

      <template #buttons>
        <v-btn
          color="primary"
          @click="editDialogController = true"
          :disabled="!isAuthorised()"
        >
          <v-icon size="16" class="pr-2">add</v-icon>
          Add New Form
        </v-btn>
      </template>

      <template #inputs>
        <v-flex md5 slot="right-aligned-content">
          <v-text-field
            appendIcon="search"
            label="Search Checklists"
            hint="Search Checklists"
            color="orange"
            solo
            flat
            class="v-solo-custom"
            clearable
            v-model="searchQuery"
          />
        </v-flex>
      </template>
    </TableTitleHeader>
    <DriverComplianceFormTable
      :driverComplianceForms="driverComplianceFormList"
      :searchQuery="searchQuery"
      @viewDriverComplianceForm="editComplianceForm"
    />

    <DriverComplianceFormEditDialog
      :key="editedComplianceForm?._id || 'new'"
      @cancelEdit="cancelEdit"
      :isDialogOpen.sync="editDialogController"
      :driverChecklist="editedComplianceForm"
      @updateChecklist="handleUpdatedComplianceForm"
    />
  </div>
</template>

<script setup lang="ts">
import DriverComplianceFormEditDialog from '@/components/admin/Administration/driver_compliance_form/driver_compliance_form_edit_dialog.vue';
import DriverComplianceFormTable from '@/components/admin/Administration/driver_compliance_form/driver_compliance_form_table.vue';
import TableTitleHeader from '@/components/common/ui-elements/table_title_header.vue';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { DriverComplianceForm } from '@/interface-models/Generic/SafetyChecklist/DriverComplianceForm';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';

import {
  computed,
  nextTick,
  onMounted,
  Ref,
  ref,
  WritableComputedRef,
} from 'vue';

const driverStore = useDriverDetailsStore();

const searchQuery = ref('');
const editChargeDialogIsOpen: Ref<boolean> = ref(false);
const editedComplianceForm: Ref<DriverComplianceForm | null> = ref(null);
const driverComplianceFormList: Ref<DriverComplianceForm[]> = ref([]);

/**
 * Controls visibility of edit compliance form dialog
 */
const editDialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return editChargeDialogIsOpen.value;
  },
  set(value: boolean): void {
    if (!value) {
      editedComplianceForm.value = null;
    }
    editChargeDialogIsOpen.value = value;
  },
});

/**
 * For the provided id, finds the associated DriverComplianceForm and sets it to
 * the edit variable, which will open the edit dialog.
 * @param {string} id - The ID of the DriverComplianceForm item.
 */
function editComplianceForm(id: string): void {
  const form: DriverComplianceForm | undefined =
    driverComplianceFormList.value.find(
      (item: DriverComplianceForm) => item._id === id,
    );
  if (!form) {
    return;
  }
  openComplianceFormInDialog(form);
}

/**
 * Opens the edit dialog with the selected Driver Compliance Form item.
 * Assigns the selected item to `editedComplianceForm` and triggers the dialog.
 *
 * @param {DriverComplianceForm} item - The driver compliance form item to edit.
 */
function openComplianceFormInDialog(item: DriverComplianceForm): void {
  editedComplianceForm.value = item;
  nextTick(() => {
    editDialogController.value = true;
  });
}

/**
 * Cancels the edit operation.
 */
function cancelEdit() {
  editedComplianceForm.value = null;
}

/**
 * Updates the saved driver compliance form in the table. If the form already
 * exists in the list, it replaces the existing entry. If it does not exist, it
 * adds the form to the list.
 *
 * @param {DriverComplianceForm} form - The updated driver compliance form
 * to be saved.
 */
function handleUpdatedComplianceForm(form: DriverComplianceForm) {
  const index = driverComplianceFormList.value.findIndex(
    (item) => item._id === form._id,
  );

  if (index !== -1) {
    driverComplianceFormList.value.splice(index, 1, { ...form });
  } else {
    driverComplianceFormList.value.push(form);
  }
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

/**
 * Fetches all driver compliance checklists from the backend when the component is mounted.
 * Updates the `driverComplianceFormList` state with the retrieved data.
 */
onMounted(async () => {
  const result = await driverStore.requestAllDriverComplianceForms();
  if (result) {
    driverComplianceFormList.value = result;
  }
});
</script>

<style scoped lang="scss">
.top-panel {
  position: fixed;
  transition: 0.2s;
  background-color: var(--background-color-400);
  top: 40px;
  height: 49px;
  left: calc(25% + 37px);
  width: calc(75% - 37px);
  border-bottom: 1px solid $translucent;
  padding: 0 8px;
  display: flex;
  align-items: center;
}
</style>
