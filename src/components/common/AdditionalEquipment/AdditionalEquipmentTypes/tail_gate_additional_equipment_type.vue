<template>
  <section class="tail-gate-details">
    <v-layout wrap>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">1. Identifying Information</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Type:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              label="Tail Gate Type"
              v-model="tailGateDetails.type"
              :items="tailGateTypes"
              item-text="longName"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              item-value="id"
              :disabled="!isEdited"
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Size:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              label="Tail Gate Size"
              type="number"
              v-model.number="tailGateDetails.size"
              :items="tailGateSizeTypes"
              item-text="longName"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              item-value="id"
              :disabled="!isEdited"
            >
            </v-select>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Height (m):</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              label="Tail Gate Height (m)"
              v-model.trim="tailGateDetails.height"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              :disabled="!isEdited"
            >
            </v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="form-field-label-container">
              <h6 class="subheader--faded pr-3 pb-0">Tonne (t):</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              label="Tail Gate Tonne (t)"
              v-model.trim="tailGateDetails.tonne"
              class="v-solo-custom"
              solo
              flat
              color="light-blue"
              :disabled="!isEdited"
            >
            </v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
    <v-flex md12 pb-3
      ><v-layout align-center>
        <h5 class="subheader--bold pr-3 pt-1">2. Compliance</h5>
        <v-flex>
          <v-divider></v-divider>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-flex md12 pb-2>
      <v-layout>
        <v-flex md4>
          <v-layout align-center class="form-field-label-container">
            <h6 class="subheader--faded pr-3 pb-0">Compliance Checks:</h6>
          </v-layout>
        </v-flex>
        <v-flex md8>
          <v-checkbox
            v-model="tailGateDetails.hasComplianceRequirement"
            :disabled="!isEdited"
            label="Requires Compliance Checks"
            hint="This Tail Gate must be kept up to date with Services"
            persistent-hint
            color="light-blue"
            class="mt-2"
          ></v-checkbox>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-flex md12 pt-2>
      <v-layout>
        <v-flex md4>
          <v-layout align-center class="form-field-label-container">
            <h6
              class="subheader--faded pr-3 pb-0"
              :class="{
                'form-field-required-marker':
                  tailGateDetails.hasComplianceRequirement,
              }"
            >
              Next Service:
            </h6>
          </v-layout>
        </v-flex>
        <v-flex md8>
          <DateTimeInputs
            :epochTime.sync="tailGateDetails.tailGateService"
            :enableValidation="true"
            :type="DateTimeType.DATE_START_OF_DAY"
            dateLabel="Date of Next Service"
            :readOnly="!isEdited"
            :boxInput="false"
            :soloInput="true"
            :isRequired="tailGateDetails.hasComplianceRequirement"
            :hintTextType="HintTextType.LABEL"
          ></DateTimeInputs>
        </v-flex>
      </v-layout>
    </v-flex>
    <v-flex md12>
      <v-layout>
        <v-flex md4>
          <v-layout align-center class="form-field-label-container">
            <h6 class="subheader--faded pr-3 pb-0">Compliance Certificate:</h6>
          </v-layout>
        </v-flex>
        <v-flex md8>
          <file-upload
            :imageLabel="'COMPLIANCE CERTIFICATE'"
            :attachmentSingle="false"
            :documentTypeId="attachmentTypes.COMPLIANCE_CERTIFICATE"
            :attachmentArray="tailGateDetails.attachments"
            :formDisabled="!isEdited"
          >
          </file-upload>
        </v-flex>
      </v-layout>
    </v-flex>

    <v-layout v-if="isEdited">
      <ConfirmationDialog
        buttonText="Delete Tail Gate Details"
        title="Delete Confirmation"
        message="Are you sure you wish to remove this Tail Gate and all associated information?"
        @confirm="remove"
        :isOutlineButton="true"
        buttonColor="red"
        confirmationButtonText="Confirm Deletion"
        :dialogIsActive="true"
      ></ConfirmationDialog>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import FileUpload from '@/components/common/file-upload/file_upload.vue';
import TailGateDetails from '@/interface-models/FleetAsset/models/AdditionalEquipmentItem/AdditionalEquipmentObjects/TailGateDetails';
import { tailGateSizeTypes } from '@/interface-models/FleetAsset/static/AdditionalEquipment/TailGateSizeTypes';
import { tailGateTypes } from '@/interface-models/FleetAsset/static/AdditionalEquipment/TailGateTypes';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';

import DateTimeInputs, {
  DateTimeType,
  HintTextType,
} from '@/components/common/date-picker/date_time_inputs.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';

const emit = defineEmits(['removeEquipmentItem']);
const attachmentTypes = AttachmentTypes;

const props = withDefaults(
  defineProps<{
    tailGateDetails: TailGateDetails | any;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

function remove(): void {
  emit('removeEquipmentItem', 2);
}
</script>
