<template>
  <ContentDialog
    :showDialog.sync="dialogController"
    :title="
      !editedAdditionalChargeItem?._id
        ? 'New Additional Charge'
        : 'Edit Additional Charge'
    "
    width="50%"
    contentPadding="pa-0"
    @cancel="dialogController = false"
    @confirm="saveAdditionalCharge"
    :showActions="true"
    :isConfirmUnsaved="false"
    :isDisabled="isFormDisabled"
    :isLoading="isAwaitingSaveResponse"
    confirmBtnText="Confirm"
  >
    <v-layout>
      <v-flex
        md12
        class="body-scrollable--75 pa-3"
        v-if="editedAdditionalChargeItem !== null"
      >
        <v-form
          ref="additionalChargesDialogForm"
          class="additional-charges-edit-dialog"
        >
          <!-- <v-alert :value="showFormErrors" class="mt-3 mb-2" type="error">
            A charge category must be selected. Please create one first if none
            exist
          </v-alert> -->

          <v-layout wrap>
            <v-flex md12 pb-3
              ><v-layout align-center>
                <h5 class="subheader--bold pr-3 pt-1">Key Details</h5>
                <v-flex>
                  <v-divider></v-divider>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Charge Category
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-select
                    class="v-solo-custom"
                    solo
                    flat
                    color="light-blue"
                    :disabled="
                      isFormDisabled || !!editedAdditionalChargeItem._id
                    "
                    label="Select Charge Category For this Charge Item"
                    :items="additionalChargeTypes"
                    item-text="longName"
                    item-value="_id"
                    v-model="selectedAdditionalChargeTypeId"
                    :rules="[validate.required]"
                  />
                </v-flex>
              </v-layout>
            </v-flex>

            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6
                      class="subheader--faded pr-3 pb-0 form-field-required-marker"
                    >
                      Charge Name
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-text-field
                    class="v-solo-custom"
                    solo
                    flat
                    color="light-blue"
                    :disabled="isFormDisabled || isTollAdminAndHandlingCharge"
                    label="Charge Name"
                    v-model="editedAdditionalChargeItem.longName"
                    :rules="[validate.required]"
                    hint="This will appear on the invoice"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">
                      Set Validity Period
                    </h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-checkbox
                    v-model="expiryDateController"
                    :disabled="isFormDisabled || isTollAdminAndHandlingCharge"
                    :label="expiryDateCheckboxLabel"
                    color="light-blue"
                    class="mt-2"
                  />
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md12 v-if="expiryDateController">
              <v-layout wrap>
                <v-flex md12 pb-3
                  ><v-layout align-center>
                    <h5 class="subheader--bold pr-3 pt-1">
                      Valid From/To Dates
                    </h5>
                    <v-flex>
                      <v-divider></v-divider>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="subheader--faded pr-3 pb-0">Valid From</h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <DatePickerBasic
                        @setEpoch="setValidFromDate"
                        :soloInput="true"
                        :labelName="'Valid From Date'"
                        :formDisabled="isFormDisabled"
                        :epochTime="editedAdditionalChargeItem.validFromDate"
                        :clearable="true"
                      />
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-layout>
                    <v-flex md4>
                      <v-layout align-center class="form-field-label-container">
                        <h6 class="subheader--faded pr-3 pb-0">Valid To</h6>
                      </v-layout>
                    </v-flex>
                    <v-flex md8>
                      <DatePickerBasic
                        @setEpoch="setValidToDate"
                        :soloInput="true"
                        :labelName="'Valid To Date'"
                        :formDisabled="isFormDisabled"
                        :epochTime="editedAdditionalChargeItem.validToDate"
                        :clearable="true"
                      />
                    </v-flex>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>

            <AdditionalChargesRateConfigForm
              :isFormDisabled="
                isFormDisabled || !editedAdditionalChargeItem.typeReferenceId
              "
              :entityType="RateEntityType.CLIENT"
              :rateConfig="editedAdditionalChargeItem.client"
              :additionalChargeType="selectedAdditionalChargeType"
              :isTollAdminAndHandlingCharge="isTollAdminAndHandlingCharge"
            ></AdditionalChargesRateConfigForm>

            <AdditionalChargesRateConfigForm
              v-if="!isTollAdminAndHandlingCharge"
              :isFormDisabled="
                isFormDisabled || !editedAdditionalChargeItem.typeReferenceId
              "
              :entityType="RateEntityType.FLEET_ASSET"
              :rateConfig="editedAdditionalChargeItem.fleetAsset"
              :additionalChargeType="selectedAdditionalChargeType"
              :isTollAdminAndHandlingCharge="isTollAdminAndHandlingCharge"
            ></AdditionalChargesRateConfigForm>
          </v-layout>
        </v-form>
      </v-flex>
    </v-layout>
  </ContentDialog>
</template>

<script setup lang="ts">
import AdditionalChargesRateConfigForm from '@/components/admin/Administration/additional_charges_index/components/additional_charges_rate_config_form.vue';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import { initialiseAdditionalChargeItem } from '@/helpers/classInitialisers/InitialiseAdditionalChargeItem';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  GENERIC_SUCCESS_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { setChargePropertiesFromType } from '@/helpers/RateHelpers/AdditionalChargeHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import type { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { useRootStore } from '@/store/modules/RootStore';
import {
  computed,
  ComputedRef,
  Ref,
  ref,
  watch,
  WritableComputedRef,
} from 'vue';

const props = withDefaults(
  defineProps<{
    isDialogOpen: boolean;
    additionalChargeItem: AdditionalChargeItem | null;
    additionalChargeTypes: AdditionalChargeType[];
    isTollAdminAndHandlingCharge: boolean;
  }>(),
  {
    additionalChargeItem: null,
    additionalChargeTypes: () => [],
  },
);

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
}>();

const validate = validationRules;

const chargeHasExpiryDate: Ref<boolean> = ref(false);

const editedAdditionalChargeItem: Ref<AdditionalChargeItem | null> = ref(null);
const isAwaitingSaveResponse: Ref<boolean> = ref(false);

const additionalChargesDialogForm: Ref<any> = ref(null);

/**
 * Gets and sets the isDialogOpen prop passed from parent
 */
const dialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.isDialogOpen;
  },
  set(value: boolean): void {
    emit('update:isDialogOpen', value);
  },
});

/**
 * V-modelled to a checkbox in the template to control visibility of the date
 * picker fields.
 */
const expiryDateController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    if (props.isTollAdminAndHandlingCharge) {
      return false;
    }
    return chargeHasExpiryDate.value;
  },
  set(value: boolean): void {
    if (editedAdditionalChargeItem.value) {
      if (value) {
        editedAdditionalChargeItem.value.validFromDate ||=
          returnStartOfDayFromEpoch();
        editedAdditionalChargeItem.value.validToDate ||=
          returnEndOfDayFromEpoch();
      } else {
        editedAdditionalChargeItem.value.validToDate = null;
      }
    }
    chargeHasExpiryDate.value = value;
  },
});

/**
 * The label for the expiry date checkbox. If valid from/to dates are set, it
 * will return a string describing those dates. Otherwise it will return a
 * string saying the charge does not expire.
 */
const expiryDateCheckboxLabel: ComputedRef<string> = computed(() => {
  if (!editedAdditionalChargeItem.value) {
    return '';
  }
  if (expiryDateController.value) {
    if (
      editedAdditionalChargeItem.value.validFromDate &&
      editedAdditionalChargeItem.value.validToDate
    ) {
      return `Valid from ${returnFormattedDate(
        editedAdditionalChargeItem.value.validFromDate,
      )} to ${returnFormattedDate(
        editedAdditionalChargeItem.value.validToDate,
      )}`;
    } else if (
      editedAdditionalChargeItem.value.validFromDate &&
      !editedAdditionalChargeItem.value.validToDate
    ) {
      return `Valid from ${returnFormattedDate(
        editedAdditionalChargeItem.value.validFromDate,
      )}`;
    } else if (
      !editedAdditionalChargeItem.value.validFromDate &&
      editedAdditionalChargeItem.value.validToDate
    ) {
      return `Valid to ${returnFormattedDate(
        editedAdditionalChargeItem.value.validToDate,
      )}`;
    }
  }
  return 'Charge does not expire';
});

/**
 * Used in the template for AdditionalChargeType v-select. When the selected
 * type ID changes, we also update the additional charge item to make sure the
 * selections are valid for the new AdditionalChargeType.
 */
const selectedAdditionalChargeTypeId: WritableComputedRef<string | undefined> =
  computed({
    get(): string | undefined {
      return editedAdditionalChargeItem.value?.typeReferenceId;
    },
    set(value: string | undefined): void {
      if (!editedAdditionalChargeItem.value) {
        return;
      }
      if (value) {
        const type = props.additionalChargeTypes.find((t) => t._id === value);
        if (type) {
          setChargePropertiesFromType(editedAdditionalChargeItem.value, type);
        }
      }
    },
  });

/**
 * The selected additional charge type based on the selected type ID. Passed
 * into the rate config forms to restrict what options are available based on
 * the category.
 */
const selectedAdditionalChargeType: ComputedRef<
  AdditionalChargeType | undefined
> = computed(() => {
  return props.additionalChargeTypes.find(
    (t) => t._id === selectedAdditionalChargeTypeId.value,
  );
});

const isFormDisabled: ComputedRef<boolean> = computed(() => {
  return isAwaitingSaveResponse.value || !isAuthorised.value;
});

/**
 * When the visibility of the dialog changes, sets the editedPudItem to a clone
 * of the additionalChargeItem prop for use in the form
 */
watch(dialogController, (newValue) => {
  if (newValue && props.additionalChargeItem) {
    const toEdit = initialiseAdditionalChargeItem(props.additionalChargeItem);
    chargeHasExpiryDate.value = !!toEdit.validToDate;
    editedAdditionalChargeItem.value = toEdit;
  } else {
    editedAdditionalChargeItem.value = null;
  }
});

/**
 * Sets the valid from date for the additional charge item.
 * @param {number} epoch - The epoch time to set as the valid from date.
 */
function setValidFromDate(epoch: number): void {
  if (editedAdditionalChargeItem.value !== null) {
    editedAdditionalChargeItem.value.validFromDate = epoch;
  }
}

/**
 * Sets the valid to date for the additional charge item.
 * @param {number} epoch - The epoch time to set as the valid to date.
 */
function setValidToDate(epoch: number): void {
  if (editedAdditionalChargeItem.value !== null) {
    editedAdditionalChargeItem.value.validToDate = epoch;
  }
}

/**
 * Saves an additional charge type or item.
 * This function checks if an additional charge type or item is currently being edited.
 * If an additional charge type is being edited, it sends a save request for the type.
 * If an additional charge item is being edited, it validates the item and sends a save request for the item.
 * After a successful save, it shows a success notification and resets the edit state.
 * @returns {Promise<void>} Resolves when the save operation is complete.
 * @throws {Error} If the save request fails, an error will be thrown.
 */
async function saveAdditionalCharge(): Promise<void> {
  if (!editedAdditionalChargeItem.value) {
    return;
  }
  if (!additionalChargesDialogForm.value?.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE, {
      title: 'Additional Charge Maintenance',
      type: HealthLevel.ERROR,
    });
    return;
  }

  isAwaitingSaveResponse.value = true;

  // TODO: Add some validation for additional charge type
  const result = await useRootStore().saveAdditionalChargeItem(
    editedAdditionalChargeItem.value,
  );
  if (result) {
    showNotification(GENERIC_SUCCESS_MESSAGE, {
      title: 'Additional Charge Maintenance',
      type: HealthLevel.SUCCESS,
    });
  } else {
    showNotification(GENERIC_ERROR_MESSAGE, {
      title: 'Additional Charge Maintenance',
      type: HealthLevel.ERROR,
    });
  }
  isAwaitingSaveResponse.value = false;
  dialogController.value = false;
}

const isAuthorised: ComputedRef<boolean> = computed(() => {
  return hasAdminOrHeadOfficeRole();
});
</script>

<style scoped lang="scss">
.additional-charges-edit-dialog {
  .subsection__header {
    font-size: $font-size-large;
    font-weight: 600;
    color: grey;
    text-transform: uppercase;
  }
}
</style>
