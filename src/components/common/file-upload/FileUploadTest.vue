<template>
  <div>
    <FileUpload
      :documentTypeId="1"
      :attachmentArray="[]"
      :attachment="singleAttachment"
      :attachmentSingle="true"
      imageLabel="Profile Picture Upload"
      :formDisabled="false"
      :isLoading="isLoading"
      @update:isLoading="isLoading = $event"
      @resetAttachmentData="resetSingleAttachment"
      @setAttachment="setSingleAttachment"
    />

    <!-- Multiple File Upload -->

    <FileUpload
      :documentTypeId="2"
      :attachmentArray="multipleAttachments"
      :attachment="new Attachment()"
      :attachmentSingle="false"
      imageLabel="Document Collection"
      :formDisabled="false"
      :isLoading="isLoading"
      @update:isLoading="isLoading = $event"
      @resetAttachmentData="resetMultipleAttachments"
    />
  </div>
</template>

<script setup lang="ts">
import { Attachment } from '@/interface-models/Generic/Attachment/Attachment';
import { computed, ref, Ref } from 'vue';
import FileUpload from './file_upload.vue';

const isLoading: Ref<boolean> = ref(false);
const singleAttachment: Ref<Attachment> = ref(new Attachment());
const multipleAttachments: Ref<Attachment[]> = ref([]);

// Computed properties
const totalFiles = computed(() => {
  let count = 0;
  if (singleAttachment.value.id) {
    count++;
  }
  count += multipleAttachments.value.length;
  return count;
});

const imageFiles = computed(() => {
  let count = 0;
  if (singleAttachment.value.mimeType?.startsWith('image/')) {
    count++;
  }
  count += multipleAttachments.value.filter(
    (f) => f.mimeType?.startsWith('image/'),
  ).length;
  return count;
});

const documentFiles = computed(() => {
  return totalFiles.value - imageFiles.value;
});

const totalSize = computed(() => {
  let size = 0;
  if (singleAttachment.value.size) {
    size += singleAttachment.value.size;
  }
  multipleAttachments.value.forEach((f) => {
    if (f.size) {
      size += f.size;
    }
  });
  return size;
});

// Methods
function resetSingleAttachment() {
  singleAttachment.value = new Attachment();
}

function setSingleAttachment(attachment: Attachment | null) {
  if (attachment) {
    singleAttachment.value = attachment;
  } else {
    resetSingleAttachment();
  }
}

function resetMultipleAttachments() {
  multipleAttachments.value = [];
}

function clearAll() {
  resetSingleAttachment();
  resetMultipleAttachments();
  isLoading.value = false;
}

function simulateLoading() {
  isLoading.value = true;
  setTimeout(() => {
    isLoading.value = false;
  }, 3000);
}

function addSampleFiles() {
  // Add sample files for demonstration
  const sampleFiles = [
    {
      id: 'sample-1',
      name: 'sample-document.pdf',
      mimeType: 'application/pdf',
      data: 'data:application/pdf;base64,sample',
      size: 1024000,
      documentTypeId: 2,
      signatureName: '',
      gpsLocation: [],
      timestamp: Date.now(),
      approvalStatus: [],
    },
    {
      id: 'sample-2',
      name: 'sample-image.jpg',
      mimeType: 'image/jpeg',
      data: 'data:image/jpeg;base64,sample',
      size: 512000,
      documentTypeId: 2,
      signatureName: '',
      gpsLocation: [],
      timestamp: Date.now(),
      approvalStatus: [],
    },
  ];

  multipleAttachments.value.push(...sampleFiles);
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) {
    return '0 B';
  }
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>

<style scoped lang="scss">
.test-header {
  text-align: center;
  padding: 20px 0;
}

.feature-list {
  list-style: none;
  padding: 0;

  li {
    padding: 4px 0;
    font-size: 0.95rem;
  }
}

.gap-2 > * {
  margin-right: 8px;
  margin-bottom: 8px;
}

.v-card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
}
</style>
