import moment from 'moment-timezone';
import AttachmentApproval from './AttachmentApproval';

export interface AttachmentInterface {
  id: string;
  documentTypeId: number;
  mimeType: string;
  data: string;
  name: string;
  signatureName: string;
  gpsLocation: number[];
  timestamp: number; // (epoch time)
  approvalStatus: AttachmentApproval[];
  size?: number; // File size in bytes (optional for backward compatibility)
}

export class Attachment implements AttachmentInterface {
  constructor(
    public id: string = '',
    public documentTypeId: number = 0,
    public mimeType: string = '',
    public data: string = '',
    public name: string = '',
    public signatureName: string = '',
    public gpsLocation: number[] = [],
    public timestamp: number = moment().valueOf(),
    // We don't need timezone if we're just getting millisecond since epoch. We only need to be concerned with timezone when we are converting to and from.
    public approvalStatus: AttachmentApproval[] = [],
    public size?: number,
  ) {}

  get currentApprovalStatus(): AttachmentApproval | undefined {
    if (
      this.approvalStatus.length === null ||
      this.approvalStatus.length === 0
    ) {
      return;
    }
    let mostRecent: AttachmentApproval = this.approvalStatus[0];
    for (const item of this.approvalStatus) {
      if (
        item.epochTime !== null &&
        mostRecent.epochTime !== null &&
        item.epochTime > mostRecent.epochTime
      ) {
        mostRecent = item;
      }
    }
    return mostRecent;
  }
}

export default Attachment;
