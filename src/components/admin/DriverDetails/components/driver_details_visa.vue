<template>
  <div class="driver-details-visa-container">
    <v-layout wrap>
      <v-flex md8 offset-md4 class="mb-4">
        <v-layout justify-end :class="{ 'disable-pointer-events': !isEdited }">
          <FileUpload
            :imageLabel="'VISA ATTACHMENT'"
            :attachmentSingle="false"
            :documentTypeId="attachmentTypes.VISA_ATTACHMENT"
            :attachmentArray="visaDetails.attachments"
          />
        </v-layout>
      </v-flex>
      <v-flex md12 pb-3
        ><v-layout align-center>
          <h5 class="subheader--bold pr-3 pt-1">Visa Details</h5>
          <v-flex>
            <v-divider></v-divider>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Document Type:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :rules="[validate.required]"
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              :disabled="!isEdited"
              v-model="visaDetails.documentType"
              :items="visaDocumentTypes"
              solo
              flat
              class="v-solo-custom"
              item-text="longName"
              item-value="id"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Reference Type:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              :disabled="!isEdited"
              v-model="visaDetails.referenceType"
              :items="visaReferenceTypes"
              solo
              class="v-solo-custom"
              flat
              item-text="longName"
              item-value="id"
              :rules="[validate.required]"
            />
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Document Number:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-text-field
              :rules="[validate.required]"
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              v-model="visaDetails.documentNumber"
              solo
              class="v-solo-custom"
              flat
              :disabled="!isEdited"
            >
            </v-text-field>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12>
        <v-layout>
          <v-flex md4>
            <v-layout align-center class="label-container">
              <h6 class="subheader--faded pr-3 pb-0">Country:</h6>
            </v-layout>
          </v-flex>
          <v-flex md8>
            <v-select
              :rules="[validate.required]"
              :class="!isEdited ? 'solo-input-disable-display' : ''"
              :disabled="!isEdited"
              v-model="visaDetails.countryOfDocument"
              solo
              flat
              class="v-solo-custom"
              :items="countries"
              item-text="name"
              item-value="name"
            />
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import FileUpload from '@/components/common/file-upload/file_upload.vue';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import VisaDetails from '@/interface-models/Driver/DriverDetails/AdditionalObjects/VisaDetails';
import countries from '@/interface-models/Generic/Addressing/Country';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import { Validation } from '@/interface-models/Generic/Validation';
import { visaDocumentTypes } from '@/interface-models/Generic/VisaDocumentTypes/VisaDocumentTypes';
import { visaReferenceTypes } from '@/interface-models/Generic/VisaReferenceTypes/VisaReferenceTypes';
import { computed, Ref } from 'vue';

const props = withDefaults(
  defineProps<{
    visaDetails: VisaDetails;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

// Define computed properties
const attachmentTypes = AttachmentTypes;

const validate: Ref<Validation> = computed(() => validationRules);
</script>

<style scoped lang="scss">
.label-container {
  height: 48px;
}
</style>
