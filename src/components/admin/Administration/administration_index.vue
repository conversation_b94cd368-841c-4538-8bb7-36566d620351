<template>
  <section id="administration-index-page" class="administration-index-page">
    <v-layout>
      <v-flex md3 class="admin-navigation-panel">
        <div class="left-panel">
          <v-toolbar dark flat dense class="header-bar">
            <h2>{{ companyDivision }} Administration</h2>
          </v-toolbar>
          <div class="scrollable">
            <v-list class="nav-list" dense>
              <v-layout v-for="item in selectOptions" :key="item.id">
                <v-flex md12 v-if="item.children.length === 0">
                  <v-list-tile
                    :disabled="item.disabled"
                    @click="selectView(item.id)"
                    class="nav-list-item"
                    :class="{ 'is-active': selectedView === item.id }"
                  >
                    <v-list-tile-action>
                      <v-icon
                        class="nav-list-item__icon"
                        v-if="item.icon"
                        size="16"
                        >{{ item.icon }}</v-icon
                      >
                    </v-list-tile-action>
                    <v-list-tile-content>
                      <v-list-tile-title>{{ item.title }}</v-list-tile-title>
                    </v-list-tile-content>
                  </v-list-tile>
                </v-flex>
                <v-flex md12 v-else>
                  <v-subheader style="height: 22px">
                    <span class="nav-title"> {{ item.title }}</span>
                  </v-subheader>
                  <v-list class="nav-list">
                    <v-list-tile
                      :disabled="subItem.disabled"
                      v-for="subItem in item.children"
                      :key="subItem.id"
                      @click="selectView(subItem.id)"
                      class="nav-list-item"
                      :class="{ 'is-active': selectedView === subItem.id }"
                    >
                      <v-list-tile-action v-if="$vuetify.breakpoint.lgAndUp">
                        <v-icon
                          class="nav-list-item__icon"
                          v-if="subItem.icon"
                          size="16"
                          :disabled="subItem.disabled"
                          >{{ subItem.icon }}</v-icon
                        >
                      </v-list-tile-action>
                      <v-list-tile-content>
                        <v-list-tile-title>
                          <span>{{ subItem.title }}</span>
                        </v-list-tile-title>
                      </v-list-tile-content>
                      <v-list-tile-action> </v-list-tile-action>
                    </v-list-tile>
                  </v-list>
                </v-flex>
              </v-layout>
            </v-list>
          </div>
        </div>
      </v-flex>
      <v-flex md9 class="middle-section">
        <v-layout class="middle-scrollable">
          <v-flex md12 class="pa-0">
            <v-flex v-if="selectedView === AdministrationViewEnum.SRD">
              <div class="px-3">
                <DivisionServiceRateAdministration
                  :isDefault="true"
                  entityId="0"
                  :type="RateEntityType.CLIENT"
                  :key="'clientRateDefault'"
                />
              </div>
            </v-flex>
            <v-flex v-if="selectedView === AdministrationViewEnum.CSR">
              <div class="px-3">
                <DivisionServiceRateAdministration
                  :isDefault="true"
                  entityId="CS"
                  :type="RateEntityType.CLIENT"
                  :key="'cashSalesDefault'"
                />
              </div>
            </v-flex>
            <!-- FLEET ASSET Service Rates -->
            <v-flex v-if="selectedView === AdministrationViewEnum.FSRD">
              <div class="px-3">
                <DivisionServiceRateAdministration
                  :isDefault="true"
                  entityId="0"
                  :type="RateEntityType.FLEET_ASSET"
                  :key="'fleetAssetRateDefault'"
                />
              </div>
            </v-flex>
            <v-flex v-if="selectedView === AdministrationViewEnum.CFS">
              <div class="px-3">
                <FuelSurchargeLevyDetails
                  :componentType="FuelComponentType.CASH_SALE"
                  key="cashSaleFuelSurcharge"
                />
              </div>
            </v-flex>

            <v-flex v-if="selectedView === AdministrationViewEnum.SRVA">
              <div class="px-3">
                <ServiceRateVariations />
              </div>
            </v-flex>

            <v-flex v-if="selectedView === AdministrationViewEnum.FSL">
              <div class="px-3">
                <FuelSurchargeLevyDetails
                  :componentType="FuelComponentType.CLIENT"
                  key="clientFuelSurcharge"
                />
              </div>
            </v-flex>

            <!-- FLEET ASSET FUEL SURCHARGE LEVY -->
            <v-flex v-if="selectedView === AdministrationViewEnum.FAFSL">
              <div class="px-3">
                <FuelSurchargeLevyDetails
                  :componentType="FuelComponentType.FLEET_ASSET"
                  key="fleetAssetFuelSurcharge"
                />
              </div>
            </v-flex>

            <!-- REPORTS -->
            <v-flex v-if="selectedView === AdministrationViewEnum.REP">
              <div class="px-3">
                <ReportIndex />
              </div>
            </v-flex>

            <!-- SALES MANAGEMENT -->
            <v-flex
              md12
              v-if="
                selectedView === AdministrationViewEnum.SAM &&
                isAuthorisedSalesManagement()
              "
            >
              <div class="px-3">
                <SalesManagement />
              </div>
            </v-flex>

            <!-- NATIONAL CLIENT -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.NCID">
              <div class="px-3">
                <NationalClientIdentifier />
              </div>
            </v-flex>

            <!-- COMMON ADDRESS -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.CCA">
              <div class="px-3">
                <CommonAddressList />
              </div>
            </v-flex>

            <!-- COMPANY USER MANAGEMENT -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.UAM">
              <div class="px-3">
                <CompanyUserManagement />
              </div>
            </v-flex>

            <!-- ACCOUNT RECOVER -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.ARM">
              <v-flex class="px-3">
                <AccountRecoveryManagement />
              </v-flex>
            </v-flex>

            <!-- ADJUSTMENT CHARGES -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.AJC">
              <div class="px-3">
                <AdjustmentCharges />
              </div>
            </v-flex>

            <!--- OPERATIONS CHANNEL ADMINISTRATION -->
            <v-flex md12 v-if="selectedView === 'CHAN'">
              <div class="px-3">
                <OperationsChannelAdministrationIndex />
              </div>
            </v-flex>

            <!-- AUDIT HISTORY -->
            <v-flex
              md12
              v-if="
                selectedView === AdministrationViewEnum.AUD && isAuthorised()
              "
            >
              <div class="px-3">
                <AuditHistoryComponent />
              </div>
            </v-flex>

            <!-- ADDITIONAL CHARGES -->
            <v-flex v-if="selectedView === AdministrationViewEnum.ADC">
              <div class="px-3">
                <AdditionalChargesIndex />
              </div>
            </v-flex>

            <!-- ADDITIONAL CHARGE CATEGORY -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.ADCC">
              <div class="px-3">
                <AdditionalChargesCategoryIndex />
              </div>
            </v-flex>

            <!-- SERVICE TYPES -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.SERV">
              <div class="px-3">
                <ServiceTypesAdministrationIndex />
              </div>
            </v-flex>

            <!-- RATE TYPES -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.RT">
              <div class="px-3">
                <RateTypesList />
              </div>
            </v-flex>

            <!-- QUOTE HISTORY -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.JQD">
              <div class="px-3">
                <QuoteDetailsList />
              </div>
            </v-flex>

            <!-- DEFAULT LOAD DURATION -->
            <v-flex v-if="selectedView === AdministrationViewEnum.DOP">
              <div class="px-3">
                <DivisionOperations />
              </div>
            </v-flex>

            <!-- INSIDE METRO SUBURBS -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.IMS">
              <div class="px-3">
                <InsideMetroSuburbs />
              </div>
            </v-flex>

            <!-- SCHEDULER PANNED RUNS -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.SCHR">
              <div class="px-3">
                <RecurringJobSchedulerMaintenance />
              </div>
            </v-flex>

            <!-- CURRENT PERMANENT JOBS -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.PERM">
              <div class="px-3">
                <RecurringJobList />
              </div>
            </v-flex>

            <!-- SMS HISTORY -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.SMS">
              <div class="px-3">
                <SmsMessageHistory />
              </div>
            </v-flex>

            <!-- EMAIL HISTORY -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.EML">
              <div class="px-3">
                <EmailMessageHistory />
              </div>
            </v-flex>

            <!-- COMPLIANCE FORMS -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.DCF">
              <div class="px-3">
                <DriverComplianceFormIndex />
              </div>
            </v-flex>

            <!-- DRIVER CHECKLIST HISTORY -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.CHK">
              <div class="px-3">
                <SafetyChecklistHistory />
              </div>
            </v-flex>

            <!-- INDUCTION TYPES -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.ID">
              <div class="px-3">
                <InductionTypesList />
              </div>
            </v-flex>

            <!-- COMPANY DETAILS -->
            <v-layout md12 v-if="selectedView === AdministrationViewEnum.CD">
              <v-flex lg8 md10 offset-lg2 offset-md1 class="pt-3">
                <CompanyDetails />
              </v-flex>
            </v-layout>

            <!-- HOLIDAY LIST -->
            <v-flex md12 v-if="selectedView === AdministrationViewEnum.HD">
              <div class="px-3">
                <HolidayDetailsList />
              </div>
            </v-flex>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import AccountRecoveryManagement from '@/components/admin/Administration/account-recovery-management/account-recovery-management.vue';
import AdditionalChargesCategoryIndex from '@/components/admin/Administration/additional_charges_category_index/additional_charges_category_index.vue';
import AdditionalChargesIndex from '@/components/admin/Administration/additional_charges_index/additional_charges_index.vue';
import AdjustmentCharges from '@/components/admin/Administration/adjustment-charges/adjustment_charges.vue';
import AuditHistoryComponent from '@/components/admin/Administration/audit_history/audit_history.vue';
import CommonAddressList from '@/components/admin/Administration/common_address_list/common_address_list.vue';
import CompanyDetails from '@/components/admin/Administration/company_details/company_details.vue';
import CompanyUserManagement from '@/components/admin/Administration/company_user_management/company_user_management.vue';
import DivisionOperations from '@/components/admin/Administration/division_operations/division_operations.vue';
import DivisionServiceRateAdministration from '@/components/admin/Administration/division_service_rate_administration/division_service_rate_administration.vue';
import DriverComplianceFormIndex from '@/components/admin/Administration/driver_compliance_form/driver_compliance_form_index.vue';
import EmailMessageHistory from '@/components/admin/Administration/email_message_history/email_message_history.vue';
import HolidayDetailsList from '@/components/admin/Administration/holiday_details/holiday_details_list.vue';
import InductionTypesList from '@/components/admin/Administration/induction_types_list/induction_types_list.vue';
import InsideMetroSuburbs from '@/components/admin/Administration/inside_metro_suburbs/inside_metro_suburbs.vue';
import NationalClientIdentifier from '@/components/admin/Administration/national_client_identifier/national_client_identifier.vue';
import OperationsChannelAdministrationIndex from '@/components/admin/Administration/operations_channel/operations_channel_administration_index.vue';
import QuoteDetailsList from '@/components/admin/Administration/quote_details_list/quote_details_list.vue';
import RateTypesList from '@/components/admin/Administration/rate_types_list/rate_types_list.vue';
import RecurringJobList from '@/components/admin/Administration/recurring_jobs/recurring_job_list.vue';
import RecurringJobSchedulerMaintenance from '@/components/admin/Administration/recurring_jobs/recurring_job_scheduler_maintenance.vue';
import ReportIndex from '@/components/admin/Administration/reporting/report_index/report_index.vue';
import SafetyChecklistHistory from '@/components/admin/Administration/safety_checklist_history/safety_checklist_history.vue';
import SalesManagement from '@/components/admin/Administration/sales_management/sales_management.vue';
import ServiceTypesAdministrationIndex from '@/components/admin/Administration/service_types_administration/service_types_administration_index.vue';
import SmsMessageHistory from '@/components/admin/Administration/sms_message_history/sms_message_history.vue';
import ServiceRateVariations from '@/components/admin/ClientDetails/components/client_rate_variations_table.vue';
import FuelSurchargeLevyDetails from '@/components/common/fuel_surcharge_levy_details/fuel_surcharge_levy_details.vue';
import {
  hasAdminOrHeadOfficeOrBranchManagerRole,
  hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole,
  hasAdminOrHeadOfficeRole,
} from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { FuelComponentType } from '@/interface-models/ServiceRates/FuelSurcharge/FuelComponentType';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { sessionManager } from '@/store/session/SessionState';
import {
  ComputedRef,
  Ref,
  computed,
  onBeforeMount,
  onBeforeUnmount,
  ref,
} from 'vue';
import { AdministrationViewEnum } from './AdministrationViewEnum';

const selectedView: Ref<AdministrationViewEnum | null> = ref(null);

const appNavigationStore = useAppNavigationStore();

const selectOptions = ref([
  {
    id: AdministrationViewEnum.RATES,
    title: 'Client Default Rates',
    icon: 'fal fa-dollar-sign',
    children: [
      {
        id: AdministrationViewEnum.SRD,
        title: 'Client Rates',
        icon: 'fal fa-dollar-sign',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.FSL,
        title: 'Client Fuel Surcharges',
        icon: 'fal fa-gas-pump',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.CSR,
        title: 'Cash Sale Rates',
        icon: 'fal fa-money-bill-alt',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.CFS,
        title: 'Cash Sale Fuel Surcharges',
        icon: 'fal fa-gas-pump',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.SRVA,
        title: 'Service Rate Variations',
        icon: 'fal fa-percentage',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
  {
    id: AdministrationViewEnum.FLEETRATES,
    title: 'Fleet Asset Default Rates',
    icon: 'fal fa-dollar-sign',
    children: [
      {
        id: AdministrationViewEnum.FSRD,
        title: 'Fleet Rates',
        icon: 'fal fa-dollar-sign',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.FAFSL,
        title: 'Fleet Fuel Surcharges',
        icon: 'fal fa-gas-pump',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
  {
    id: AdministrationViewEnum.REPADMIN,
    title: 'Reporting and Administration',
    icon: '',
    children: [
      {
        id: AdministrationViewEnum.REP,
        title: 'Reporting',
        icon: 'fal fa-chart-bar',
        children: [],
        disabled: !isAuthorisedReporting(),
      },
      {
        id: AdministrationViewEnum.SAM,
        title: 'Sales Management',
        icon: 'fal fa-piggy-bank',
        children: [],
        disabled: !isAuthorisedSalesManagement(),
      },
      {
        id: AdministrationViewEnum.NCID,
        title: 'National Client Identification',
        icon: 'fal fa-fingerprint',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.CCA,
        title: 'Client Common Address',
        icon: 'fal fa-address-card',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.UAM,
        title: 'User Account Management',
        icon: 'fal fa-user-circle',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.ARM,
        title: 'Account Recovery Management',
        icon: 'fal fa-user-circle',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.AJC,
        title: 'Adjustment Charges',
        icon: 'fal fa-file-chart-line',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.AUD,
        title: 'Audit History',
        icon: 'fal fa-pencil',
        children: [],
        disabled: !isAuthorised(),
      },
    ],
    disabled: false,
  },
  {
    id: AdministrationViewEnum.AC,
    title: 'Additional Charges',
    icon: '',
    children: [
      {
        id: AdministrationViewEnum.ADC,
        title: 'Additional Charges',
        icon: 'fal fa-receipt',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.ADCC,
        title: 'Additional Charges Category',
        icon: 'category',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
  {
    id: AdministrationViewEnum.OPS,
    title: 'Operations and Jobs',
    icon: 'fal fa-briefcase',
    children: [
      {
        id: AdministrationViewEnum.SERV,
        title: 'Service Types',
        icon: 'fal fa-th-list',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.RT,
        title: 'Rate Types',
        icon: 'fal fa-money-bill',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.JQD,
        title: 'Quote History',
        icon: 'fal fa-quote-right',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.DOP,
        title: 'Default Load Durations',
        icon: 'fal fa-briefcase',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.IMS,
        title: 'Inside Metro Suburbs',
        icon: 'fal fa-location-circle',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.CHAN,
        title: 'Operations Channels',
        icon: 'fal fa-th-list',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
  {
    id: AdministrationViewEnum.RJS,
    title: 'Permanent Jobs',
    icon: 'fal fa-calendar-alt',
    children: [
      {
        id: AdministrationViewEnum.SCHR,
        icon: 'fal fa-gas-pump',
        title: 'Scheduler Planned Runs',
      },
      {
        id: AdministrationViewEnum.PERM,
        icon: 'fal fa-calendar-alt',
        title: 'Current Permanent Jobs',
      },
    ],
    disabled: false,
  },
  {
    id: AdministrationViewEnum.MSG,
    title: 'SMS and Email Logs',
    icon: 'fal fa-mail-bulk',
    children: [
      {
        id: AdministrationViewEnum.SMS,
        icon: 'fal fa-sms',
        title: 'SMS Message History',
        disabled: !isAuthorisedReporting(),
      },
      {
        id: AdministrationViewEnum.EML,
        icon: 'fal fa-inbox-out',
        title: 'Email History',
        disabled: !isAuthorisedReporting(),
      },
    ],
    disabled: false,
  },
  {
    id: AdministrationViewEnum.COMP,
    title: 'Compliance',
    icon: 'fal fa-clipboard-list-check',
    children: [
      {
        id: AdministrationViewEnum.DCF,
        icon: 'checklist_rtl',
        title: 'Compliance Forms',
      },
      {
        id: AdministrationViewEnum.CHK,
        icon: 'fal fa-clipboard-list-check',
        title: 'Driver Checklist History',
      },
      {
        id: AdministrationViewEnum.ID,
        title: 'Induction Types',
        icon: 'fal fa-flag-checkered',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
  {
    id: AdministrationViewEnum.COMPANYDETAILS,
    title: 'Company Details',
    icon: 'fal fa-dollar-sign',
    children: [
      {
        id: AdministrationViewEnum.CD,
        title: 'Company Details',
        icon: 'fal fa-info-circle',
        children: [],
        disabled: false,
      },
      {
        id: AdministrationViewEnum.HD,
        title: 'Holiday Details',
        icon: 'fal fa-calendar',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
]);

/**
 * Returns a string representing the company and division, to be displayed in
 * the template.
 */
const companyDivision: ComputedRef<string> = computed(() => {
  return sessionManager.getCompanyId() + ' - ' + sessionManager.getDivisionId();
});

/**
 * Sets the current view to the provided id.
 * @param {string} id - The ID of the view to select.
 */
function selectView(id: AdministrationViewEnum) {
  useAppNavigationStore().setCurrentDivisionView(id);
  selectedView.value = id;
}

/**
 * Checks if the user is authorized for certain actions. For use in the template
 * to enable or disable certain views.
 * @returns {boolean} True if the user has admin or head office role, false
 * otherwise.
 */
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}
function isAuthorisedReporting(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}

/**
 * Checks if the user is authorized for certain actions. For use in the template
 * to enable or disable certain views.
 * @returns {boolean} True if the user has admin, head office, or branch manager
 * role, false otherwise.
 */
function isAuthorisedSalesManagement(): boolean {
  return hasAdminOrHeadOfficeOrBranchManagerRole();
}

onBeforeMount(() => {
  if (appNavigationStore.selectedDivisionView) {
    selectedView.value = appNavigationStore.selectedDivisionView;
    appNavigationStore.setCurrentDivisionView(null);
  } else {
    selectedView.value = AdministrationViewEnum.SRD;
  }
});

onBeforeUnmount(() => {
  appNavigationStore.setCurrentDivisionView(null);
});
</script>

<style scoped lang="scss">
.administration-index-page {
  background-color: var(--background-color-250) !important;
}

.admin-navigation-panel {
  .left-panel {
    position: fixed;
    transition: 0.2s;
    height: calc(100vh - 40px);
    width: calc(25% - 14px);
    z-index: 2;
    background-color: var(--background-color-250) !important;
    border-right: 1px solid $translucent;
  }
}

.header-bar {
  background-color: var(--background-color-400) !important;
  border-bottom: 1px solid $translucent;
  z-index: 9;
}

.nav-title {
  color: var(--bg-light);
  font-size: $font-size-12;
  font-weight: 700;
  text-transform: uppercase;
  padding: 4px;
}

.nav-title {
  color: var(--bg-light);
  font-size: $font-size-12;
  font-weight: 700;
  text-transform: uppercase;
  padding: 4px;
}

.nav-list {
  background: none !important;
  .nav-list-item {
    color: var(--text-color);
    background: none;
    transition: 0s;
    .v-list__tile__title {
      transition: 0s;
    }
    .nav-list-item__icon {
      color: var(--primary);
    }

    &.is-active {
      background-color: $bg-light;
      border-left: 5px solid var(--primary);
      color: black;

      span {
        font-size: $font-size-15;
        font-weight: 500;
      }
      .nav-list-item__icon {
        color: var(--primary);
        font-weight: 600;
      }
    }
  }
}

.middle-section {
  height: calc(100vh - 44px);
  background-color: $app-dark-primary-250 !important;

  .middle-scrollable {
    height: calc(100vh - 44px);
    max-height: calc(100vh);
    padding-top: 28px;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
    background-color: var(--background-color-250) !important;
  }
}

.scrollable {
  background-color: var(--background-color-250) !important;
  height: calc(100vh - 88px);
  max-height: calc(100vh - 88px);
  overflow-y: scroll;
  overscroll-behavior-y: contain;
}
</style>
