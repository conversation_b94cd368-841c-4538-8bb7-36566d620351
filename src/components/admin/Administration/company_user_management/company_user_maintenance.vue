<template>
  <section
    class="company-user-maintenance"
    v-if="props.companyUserWithAuthDetails"
  >
    <div class="px-3 py-3">
      <div class="flex-row justify-space-between">
        <GTitle
          class="mb-2"
          title="Contact Details"
          subtitle="The users primary contact details"
          :divider="false"
        />
        <GButton
          v-if="!isNewCompanyUser"
          rounded
          :disabled="
            isNewCompanyUser || confirmEmailUpdateLink || isCurrentUserReadOnly
          "
          @click="confirmEmailUpdateLink = true"
          :icon="'fas fa-envelope'"
          iconRight
          color="primary"
          >Update Account Details</GButton
        >
        <GDialog
          v-if="confirmEmailUpdateLink"
          :width="'500px'"
          :title="'Request to update details - confirmation'"
          :confirmBtnText="'Confirm'"
          @deleteItem="removeRole"
          @closeDialog="confirmEmailUpdateLink = false"
          @confirm="sendUpdateCompanyUserDetailsRequest"
        >
          <div class="pa-3">
            <div class="email-message">
              <p class="mb-3">
                Please confirm that you wish to email an 'Update Account
                Details' link to the following recipient:
              </p>
              <div class="mb-3">
                <p class="mb-1">
                  <b>Recipient:</b>
                  {{ props.companyUserWithAuthDetails.companyUser.firstName }}
                  {{ props.companyUserWithAuthDetails.companyUser.lastName }}
                </p>
                <p>
                  <b>Email Address:</b>
                  {{
                    props.companyUserWithAuthDetails.companyUser.emailAddress
                  }}
                </p>
              </div>
              <p>
                Please review the information above and ensure that you intend
                to send this email before proceeding. Your confirmation is
                appreciated.
              </p>
            </div>
          </div>
        </GDialog>
      </div>
      <hr class="divider" />
      <GTextField
        v-model="props.companyUserWithAuthDetails.companyUser.firstName"
        :placeholder="'First Name'"
        :rules="[rules.required]"
        :disabled="areFormFieldsDisabled"
      ></GTextField>

      <GTextField
        v-model="props.companyUserWithAuthDetails.companyUser.lastName"
        :placeholder="'Last Name'"
        :disabled="areFormFieldsDisabled"
        :rules="[rules.required]"
      ></GTextField>

      <GEmailValidator
        v-model="props.companyUserWithAuthDetails.companyUser.emailAddress"
        @setIsLoading="setIsEmailLoading"
        :disabled="
          areFormFieldsDisabled ||
          (!props.companyUserWithAuthDetails.canEditUsername &&
            !isNewCompanyUser)
        "
      />

      <GMobileValidator
        v-model="props.companyUserWithAuthDetails.companyUser.contactNumber"
        @setIsLoading="setIsMobileLoading"
        :disabled="
          areFormFieldsDisabled ||
          (!props.companyUserWithAuthDetails.canEditUsername &&
            !isNewCompanyUser)
        "
      />

      <div
        v-if="isCurrentUserReadOnly"
        class="mb-3 pa-3"
        style="
          background-color: #fff3cd;
          border: 1px solid #ffeaa7;
          border-radius: 4px;
        "
      >
        <p class="mb-0" style="color: #856404">
          <i class="fas fa-info-circle mr-2"></i>
          <strong>Read-Only Access:</strong> You have view-only permissions for
          user management. Contact an administrator to make changes to user
          roles and details.
        </p>
      </div>

      <GTable
        class="mt-4"
        :title="'Company Division Roles'"
        :subtitle="'The users current roles within the company'"
        :headers="headers"
        :items="props.companyUserWithAuthDetails.companyRoles"
        :dataRequired="isNewCompanyUser"
        :customErrorMessage="'User requires at least one role.'"
        :noDataMessage="'This user has no roles.'"
        :selectable="!isCurrentUserReadOnly"
        @selectItem="viewRole"
      >
        <v-btn
          slot="action"
          :color="'primary'"
          class="my-2"
          depressed
          round
          :disabled="isCurrentUserReadOnly"
          @click="setRoleDialog(true)"
        >
          Add Role
          <v-icon right dark small>fas fa-plus</v-icon>
        </v-btn>
        <template v-slot:items="row">
          <td>
            {{ row.item.division }}
          </td>
          <td>
            {{ row.item.roleName }}
          </td>
          <td>
            {{ row.item.status }}
          </td>

          <td>
            <div
              class="status-el"
              style="display: flex; justify-content: flex-end"
            >
              <v-tooltip left attach=".status-el">
                <template v-slot:activator="{ on }">
                  <v-btn
                    class="v-btn-rounded"
                    depressed
                    small
                    icon
                    v-on="on"
                    fab
                    :disabled="
                      row.item.status === UserRoleStatus.PENDING ||
                      isCurrentUserReadOnly
                    "
                    :color="row.item.status === 'LOCKED' ? 'accent' : 'info'"
                    @click.stop="setLockUnlockRequest(row.item)"
                    ><v-icon color="black">
                      {{
                        row.item.status === 'LOCKED'
                          ? 'fas fa-unlock-alt'
                          : 'fas fa-lock-alt'
                      }}
                    </v-icon>
                  </v-btn>
                </template>
                {{
                  row.item.status === 'LOCKED'
                    ? 'Unlock this role'
                    : 'Lock this role'
                }}
              </v-tooltip>
            </div>
          </td>
        </template>
      </GTable>

      <GDialog
        ref="roleDialogRef"
        :width="'500px'"
        :title="'Role Administration'"
        :confirmBtnText="'Add Role'"
        :isDelete="isViewingExistingRole"
        :deleteBtnText="'Remove Role'"
        :confirmDisabled="isViewingExistingRole"
        :isLoading="isLoadingRoleAddOrRemoveRequest"
        @deleteItem="removeRole"
        @closeDialog="setRoleDialog(false)"
        @confirm="addRoleToUser"
        v-if="companyRoleStatus"
      >
        <div class="pa-3">
          <GTitle
            title="Role Administration"
            subtitle="Please select the division and role you would like to apply to this user."
          />

          <div
            v-if="!isCurrentUserReadOnly"
            class="mb-3 pa-2"
            style="
              background-color: #e8f5e8;
              border: 1px solid #c3e6c3;
              border-radius: 4px;
            "
          >
            <p class="mb-0" style="color: #2d5a2d; font-size: 0.9em">
              <i class="fas fa-info-circle mr-2"></i>
              {{ roleManagementPermissionMessage }}
            </p>
          </div>

          <GSelect
            v-model="companyRoleStatus.division"
            :items="props.companyDivisionList"
            :item-text="'key'"
            :item-value="'value'"
            :placeholder="'Select a division'"
            :disabled="isViewingExistingRole || isLoadingRoleAddOrRemoveRequest"
            :label="'Select a division'"
            :rules="[rules.required]"
          />
          <GSelect
            v-model.number="companyRoleStatus.roleId"
            :items="companyUserRoles"
            :item-value="'roleId'"
            :item-text="'name'"
            :disabled="isViewingExistingRole || isLoadingRoleAddOrRemoveRequest"
            :placeholder="'Select a role type'"
            :disabledValues="[
              ...disabledRolesForSelectedDivision,
              ...disabledRoleIds,
            ]"
            :label="'Select a role type'"
            :customErrorMessage="roleErrorMessage"
            :rules="[rules.required]"
          />
        </div>
      </GDialog>

      <GDialog
        ref="roleDialogRef"
        :width="'500px'"
        :title="'Confirm Role Removal'"
        :confirmBtnText="'Confirm Removal'"
        :isDelete="false"
        :confirmDisabled="false"
        :isLoading="isLoadingRoleAddOrRemoveRequest"
        @closeDialog="confirmRoleRemovalDialog = false"
        @confirm="removeRoleConfirmed"
        v-if="confirmRoleRemovalDialog && roleRemovalConfirmationMessage"
      >
        <div class="pa-3">
          <p>
            Please verify the role to be removed and the associated user details
            below:
          </p>
          <ul>
            <li>
              Role:
              <strong>{{ roleRemovalConfirmationMessage.roleName }}</strong>
            </li>
            <li>
              Division:
              <strong>
                {{ roleRemovalConfirmationMessage.divisionName }}</strong
              >
            </li>
            <li>
              User:
              <strong>{{ roleRemovalConfirmationMessage.accountName }}</strong>
            </li>
            <li>
              Email:
              <strong>
                {{ roleRemovalConfirmationMessage.accountEmailAddress }}</strong
              >
            </li>
          </ul>
          <p class="mt-3">
            Should the details be accurate, you may proceed with removing the
            role.
          </p>
        </div>
      </GDialog>

      <GDialog
        :width="'500px'"
        :title="'Role Status Management'"
        :confirmBtnText="
          'Confirm and ' +
          (currentRoleStatus.status === UserRoleStatus.LOCKED
            ? 'unlock'
            : 'lock')
        "
        :isDelete="false"
        :confirmDisabled="false"
        :isLoading="false"
        @closeDialog="closeRoleUpdateDialog"
        @confirm="sendUpdateRoleRequest"
        v-if="isRoleStatusUpdate && currentRoleStatus"
      >
        <div class="pa-3">
          <p>
            Please verify the associated user and role update details below:
          </p>
          <ul v-if="props.companyUserWithAuthDetails">
            <li>
              User:
              <strong>{{
                props.companyUserWithAuthDetails.companyUser.firstName +
                ' ' +
                props.companyUserWithAuthDetails.companyUser.lastName
              }}</strong>
            </li>
            <li>
              Role:
              <strong>{{ currentRoleStatus.roleName }}</strong>
            </li>

            <li>
              Current Status:
              <strong>{{ currentRoleStatus.status }}</strong>
            </li>

            <li>
              Updated Status:
              <strong>{{
                currentRoleStatus.status === UserRoleStatus.LOCKED
                  ? 'ACTIVE'
                  : 'LOCKED'
              }}</strong>
            </li>
          </ul>
          <p class="mt-3">
            Should the details be accurate, you may proceed with
            <strong>{{
              currentRoleStatus.status === UserRoleStatus.LOCKED
                ? 'unlocking'
                : 'locking'
            }}</strong>
            this users {{ currentRoleStatus.roleName }} role.
          </p>
        </div>
      </GDialog>
    </div>
  </section>
</template>

<script setup lang="ts">
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  hasAdminOrHeadOfficeOrBranchManagerRole,
  hasAdminRole,
} from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import {
  validate,
  validationRules,
} from '@/helpers/ValidationHelpers/ValidationHelpers';
import CompanyRole from '@/interface-models/Admin/CompanyRole';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import {
  UserRole,
  UserRoleType,
  UserType,
} from '@/interface-models/Roles/UserRoles';
import {
  CompanyRoleStatus,
  CompanyUserUpdateDetailsResponse,
  CompanyUserWithAuthDetails,
} from '@/interface-models/User/CompanyUserDetails';
import {
  LockUnlockRequest,
  LockUnlockRoleRequest,
  UpdateRoleAccessRequest,
  UpdateRoleAccessResponse,
} from '@/interface-models/User/UpdateRoleAccess';
import { UserRoleStatus } from '@/interface-models/User/UserRoleStatus';
import { useRootStore } from '@/store/modules/RootStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { computed, ComputedRef, ref, Ref } from 'vue';

const emit = defineEmits([
  'setIsEmailVerificationLoading',
  'setIsMobileVerificationLoading',
]);
const roleDialogRef = ref(null);
const isViewingExistingRole: Ref<boolean> = ref(false);
const confirmEmailUpdateLink: Ref<boolean> = ref(false);
const confirmRoleRemovalDialog: Ref<boolean> = ref(false);
const isLoadingRoleAddOrRemoveRequest: Ref<boolean> = ref(false);
const currentRoleStatus: Ref<CompanyRoleStatus | null> = ref(null);
const isRoleStatusUpdate: Ref<boolean> = ref(false);
const isLoadingRoleUpdateRequest: Ref<boolean> = ref(false);

interface IProps {
  companyUserWithAuthDetails: CompanyUserWithAuthDetails | null;
  companyDivisionList: KeyValue[];
}
const props = withDefaults(defineProps<IProps>(), {
  companyUserWithAuthDetails: null,
  companyDivisionList: () => [],
});

/**
 * Emits an event to set the loading state of email verification.
 * @param {boolean} isLoading - Indicates whether email verification is in progress.
 */
function setIsEmailLoading(isLoading: boolean) {
  emit('setIsEmailVerificationLoading', isLoading);
}

/**
 * Emits an event to set the loading state of mobile number verification.
 * @param {boolean} isLoading - Indicates whether mobile number verification is in progress.
 */
function setIsMobileLoading(isLoading: boolean) {
  emit('setIsMobileVerificationLoading', isLoading);
}

/**
 * Defines headers for a table.
 * @type {TableHeader[]}
 */
const headers: TableHeader[] = [
  {
    text: 'Division',
    align: 'left',
    value: 'division',
  },
  {
    text: 'Role',
    align: 'left',
    value: 'roleName',
  },
  {
    text: 'status',
    align: 'left',
    value: 'status',
  },
  {
    text: 'Lock / Unlock',
    align: 'right',
    value: 'status',
  },
];

/**
 * A ref to hold the current status of a company role.
 * @type {Ref<CompanyRoleStatus | null>}
 */
const companyRoleStatus: Ref<CompanyRoleStatus | null> = ref(null);

/**
 * Sets the role dialog visibility and initializes or clears the company role status.
 * @param {boolean} isOpen - Indicates if the role dialog should be open.
 */
function setRoleDialog(isOpen: boolean) {
  if (!isOpen) {
    companyRoleStatus.value = null;
    return;
  } else {
    isViewingExistingRole.value = false;
  }
  let roleId: number = companyUserRoles.value[0].roleId;
  let roleName: string = companyUserRoles.value[0].name;

  if (props.companyUserWithAuthDetails) {
    for (const role of companyUserRoles.value) {
      const foundRole: CompanyRoleStatus | undefined =
        props.companyUserWithAuthDetails.companyRoles.find(
          (x: CompanyRoleStatus) => x.roleId === role.roleId,
        );
      if (!foundRole) {
        roleId = role.roleId;
        roleName = role.name;
      }
    }
  }

  companyRoleStatus.value = {
    division: sessionManager.getDivisionId(),
    roleName,
    roleId,
    status: UserRoleStatus.PENDING,
  };
}

/**
 * Removes a role from a new company user. Due to how the new and update apis are setup we need to handle new vs update differently.
 */
function removeRole(): void {
  if (!props.companyUserWithAuthDetails || !companyRoleStatus.value) {
    return;
  }

  // Check if current user has permission to manage roles
  if (isCurrentUserReadOnly.value) {
    showNotification('You do not have permission to remove roles from users.', {
      title: 'Access Denied',
      type: HealthLevel.ERROR,
    });
    return;
  }

  // Find the role being removed to check permissions
  const userRole: UserRole | undefined = useRootStore().roleList.find(
    (x: UserRole) => x.roleId === companyRoleStatus.value?.roleId,
  );

  if (userRole && !canManageSpecificRole(userRole.roleType as UserRoleType)) {
    showNotification(
      `You do not have permission to remove the ${userRole.name} role.`,
      {
        title: 'Access Denied',
        type: HealthLevel.ERROR,
      },
    );
    return;
  }

  // If its a new user we will allow the user to add and remove roles without a confirmation. If the user already exists we show a confirmation to the user about the request to remove an existing role.
  if (isNewCompanyUser.value) {
    const index = props.companyUserWithAuthDetails.companyRoles.findIndex(
      (x: CompanyRoleStatus) => x.roleId === companyRoleStatus.value?.roleId,
    );
    if (index < 0) {
      return;
    }
    props.companyUserWithAuthDetails.companyRoles.splice(index, 1);
    setRoleDialog(false);
  } else {
    confirmRoleRemovalDialog.value = true;
  }
}

/**
 * Sets the dialog for unlocking and locking the selected role.
 */
function setLockUnlockRequest(companyRoleStatus: CompanyRoleStatus) {
  // Check if current user has permission to manage roles
  if (isCurrentUserReadOnly.value) {
    showNotification('You do not have permission to lock/unlock user roles.', {
      title: 'Access Denied',
      type: HealthLevel.ERROR,
    });
    return;
  }

  // Find the role being locked/unlocked to check permissions
  const userRole: UserRole | undefined = useRootStore().roleList.find(
    (x: UserRole) => x.roleId === companyRoleStatus.roleId,
  );

  if (userRole && !canManageSpecificRole(userRole.roleType as UserRoleType)) {
    showNotification(
      `You do not have permission to lock/unlock the ${userRole.name} role.`,
      {
        title: 'Access Denied',
        type: HealthLevel.ERROR,
      },
    );
    return;
  }

  isRoleStatusUpdate.value = true;
  currentRoleStatus.value = companyRoleStatus;
}

/**
 * Closes the role status maintenance dialog
 */
function closeRoleUpdateDialog() {
  isRoleStatusUpdate.value = false;
  currentRoleStatus.value = null;
}

/**
 * When the user has confirmed that they wish to remove a role from an existing company user.
 */
function removeRoleConfirmed() {
  if (
    !props.companyUserWithAuthDetails ||
    !companyRoleStatus.value ||
    !props.companyUserWithAuthDetails.companyUser._id ||
    !props.companyUserWithAuthDetails.companyUser.authRefId ||
    !sessionManager.getCompanyId() ||
    !sessionManager.getDivisionId()
  ) {
    showNotification('Something went wrong.', {
      title: 'Role Maintenance',
      type: HealthLevel.ERROR,
    });
    return;
  }

  // Double-check permissions before proceeding with role removal
  if (isCurrentUserReadOnly.value) {
    showNotification('You do not have permission to remove roles from users.', {
      title: 'Access Denied',
      type: HealthLevel.ERROR,
    });
    return;
  }

  // Find the role being removed to check permissions
  const userRole: UserRole | undefined = useRootStore().roleList.find(
    (x: UserRole) => x.roleId === companyRoleStatus.value?.roleId,
  );

  if (userRole && !canManageSpecificRole(userRole.roleType as UserRoleType)) {
    showNotification(
      `You do not have permission to remove the ${userRole.name} role.`,
      {
        title: 'Access Denied',
        type: HealthLevel.ERROR,
      },
    );
    return;
  }

  updateRoleAccessRequest(
    companyRoleStatus.value,
    props.companyUserWithAuthDetails.companyUser.companyUserId,
    props.companyUserWithAuthDetails.companyUser.authRefId!,
    props.companyUserWithAuthDetails.companyUser._id,
    sessionManager.getCompanyId(),
    companyRoleStatus.value.division,
    false,
  );
}

/**
 * Update request for adding or removing a role for a specified user.
 * This function constructs the role addition or removal request based on the provided parameters
 * and sends it through a WebSocket request. It listens for a response on the operation and handles
 * loading state accordingly.
 *
 * @param {CompanyRoleStatus} targetRole - The target role's status and ID.
 * @param {string} companyUserId - The ID of the company user to which the role will be added or removed.
 * @param {string} authRefId - The authentication reference ID.
 * @param {string} companyUser_id - The company users unique mongo identifier.
 * @param {string} company - The name of the company.
 * @param {string} division - The division within the company.
 * @param {boolean} isAddRole - Flag indicating whether the role is being added (true) or removed (false).
 */
function updateRoleAccessRequest(
  targetRole: CompanyRoleStatus,
  companyUserId: string,
  authRefId: string,
  companyUser_id: string,
  company: string,
  division: string,
  isAddRole: boolean,
) {
  const roleToBeAddedOrRemoved: CompanyRole[] = [
    {
      roleId: targetRole.roleId,
      userId: companyUserId,
      referenceId: companyUser_id,
      status: isAddRole ? UserRoleStatus.ACTIVE : targetRole.status,
    },
  ];
  const updateRoleAccessRequest: UpdateRoleAccessRequest = {
    authRefId: authRefId,
    company,
    division,
    addedRoles: isAddRole ? roleToBeAddedOrRemoved : [],
    removedRoles: !isAddRole ? roleToBeAddedOrRemoved : [],
    clientId: null,
    defaultDispatcher: false,
  };

  isLoadingRoleAddOrRemoveRequest.value = true;
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      sessionManager.isClientPortal()
        ? `/user/${sessionManager.getUserName()}/authUserDetails/updateRoleAccess`
        : '/authUserDetails/updateRoleAccess',
      updateRoleAccessRequest,
      true,
    ),
  );
  const responseEvent = sessionManager.isClientPortal()
    ? 'updatedRoleAccessFromClientPortalResponse'
    : 'updatedRoleAccessResponse';
  Mitt.on(responseEvent, (response: UpdateRoleAccessResponse) => {
    if (!response) {
      showNotification('Something went wrong.', {
        title: 'Role Maintenance',
        type: HealthLevel.ERROR,
      });
    }

    if (response.updateSuccessful) {
      showNotification('Role Updated Successfully.', {
        title: 'Role Maintenance',
        type: HealthLevel.INFO,
      });

      if (
        props.companyUserWithAuthDetails &&
        props.companyUserWithAuthDetails.companyUser.authRefId ===
          response.authRefId
      ) {
        for (const newRole of response.addedRoles) {
          const foundRole = useRootStore().roleList.find(
            (x: UserRole) => x.roleId === newRole.roleId,
          );
          const companyRoleStatus: CompanyRoleStatus = {
            division: response.division,
            roleName: foundRole ? foundRole.name : '-',
            roleId: newRole.roleId,
            status: newRole.status,
          };
          props.companyUserWithAuthDetails.companyRoles.push(companyRoleStatus);
        }

        for (const removedRole of response.removedRoles) {
          const indexOfRole: number =
            props.companyUserWithAuthDetails.companyRoles.findIndex(
              (x: CompanyRoleStatus) =>
                x.roleId === removedRole.roleId &&
                x.division === response.division,
            );
          if (indexOfRole >= 0) {
            props.companyUserWithAuthDetails.companyRoles.splice(
              indexOfRole,
              1,
            );
          }
        }
      }
    }

    isLoadingRoleAddOrRemoveRequest.value = false;
    confirmRoleRemovalDialog.value = false;
    companyRoleStatus.value = null;
    Mitt.off('updatedRoleAccessResponse');
  });
}

/** Sends a WebSocket request to update company user details via email. On receiving a response, it shows a
 * notification based on the success or failure of the update. It listens for a specific response
 * event and removes the listener after handling the response.
 */
function sendUpdateCompanyUserDetailsRequest(): void {
  try {
    if (!props.companyUserWithAuthDetails) {
      return;
    }
    useWebsocketStore().sendWebsocketRequest(
      new WebSocketRequest(
        '/companyUserDetails/updateDetails',
        props.companyUserWithAuthDetails.companyUser._id,
        false,
      ),
    );
    Mitt.on(
      'sentCompanyUserUpdateDetailsRequestResponse',
      (sentUpdateEmail: CompanyUserUpdateDetailsResponse) => {
        if (sentUpdateEmail.isSuccess) {
          showNotification(
            'An email has been successfully sent to the user, prompting them to update their information.',
            {
              title: 'Company User - Update Details',
              type: HealthLevel.INFO,
            },
          );

          confirmEmailUpdateLink.value = false;
        } else {
          showNotification(
            'Failed to send the email requesting an update of details. Please try again or contact support for assistance.',
            {
              title: 'Company User - Update Details',
              type: HealthLevel.ERROR,
            },
          );
        }
        Mitt.off('sentCompanyUserUpdateDetailsRequestResponse');
      },
    );
  } catch (e) {
    console.error(e);
  }
}
/**
 * Sets up the view for an existing company role.
 * @param {CompanyRoleStatus} role - The role to be viewed.
 */
function viewRole(role: CompanyRoleStatus) {
  // Restrict access for read-only users (CSR and Team Leaders)
  if (isCurrentUserReadOnly.value) {
    showNotification('You do not have permission to view role details.', {
      title: 'Access Denied',
      type: HealthLevel.ERROR,
    });
    return;
  }

  isViewingExistingRole.value = true;
  companyRoleStatus.value = role;
}
/**
 * A computed ref that filters the role list based on user type and current user's permissions.
 * Only shows roles that the current user is allowed to assign to others.
 * @type {ComputedRef<UserRole[]>}
 */
const companyUserRoles: ComputedRef<UserRole[]> = computed(() => {
  return useRootStore().roleList.filter(
    (x: UserRole) => x.userType === UserType.COMPANY,
  );
});

/**
 * Adds a role to the company user if valid and updates the company roles list.
 */
function addRoleToUser(): void {
  if (
    !companyRoleStatus.value ||
    !props.companyUserWithAuthDetails ||
    !validate(roleDialogRef.value)
  ) {
    return;
  }

  // Check if current user has permission to manage roles
  if (isCurrentUserReadOnly.value) {
    showNotification('You do not have permission to add roles to users.', {
      title: 'Access Denied',
      type: HealthLevel.ERROR,
    });
    return;
  }

  // Find the role being added to check permissions
  const userRole: UserRole | undefined = companyUserRoles.value.find(
    (x: UserRole) => x.roleId === companyRoleStatus.value?.roleId,
  );

  if (!userRole) {
    return;
  }

  // Check if current user can manage this specific role type
  if (!canManageSpecificRole(userRole.roleType as UserRoleType)) {
    showNotification(
      `You do not have permission to assign the ${userRole.name} role.`,
      {
        title: 'Access Denied',
        type: HealthLevel.ERROR,
      },
    );
    return;
  }

  // If its a new company user, we can just add the role to the document. If we want to add a role to an existing user, we do so via api request
  if (isNewCompanyUser.value) {
    companyRoleStatus.value.roleName = userRole.name;
    props.companyUserWithAuthDetails.companyRoles.push(companyRoleStatus.value);
    companyRoleStatus.value = null;
  } else {
    if (
      !props.companyUserWithAuthDetails ||
      !companyRoleStatus.value ||
      !props.companyUserWithAuthDetails.companyUser._id ||
      !props.companyUserWithAuthDetails.companyUser.authRefId ||
      !sessionManager.getCompanyId() ||
      !sessionManager.getDivisionId()
    ) {
      showNotification('Something went wrong.', {
        title: 'Role Maintenance',
        type: HealthLevel.ERROR,
      });
      return;
    }
    updateRoleAccessRequest(
      companyRoleStatus.value,
      props.companyUserWithAuthDetails.companyUser.companyUserId,
      props.companyUserWithAuthDetails.companyUser.authRefId!,
      props.companyUserWithAuthDetails.companyUser._id,
      sessionManager.getCompanyId(),
      companyRoleStatus.value.division,
      true,
    );
  }
}

/**
 * Sends a request to update (lock/unlock) a user's role status.
 * This function constructs and sends a WebSocket request to lock or unlock a role for a user.
 */
function sendUpdateRoleRequest(): void {
  if (
    !currentRoleStatus.value ||
    !isRoleStatusUpdate.value ||
    !props.companyUserWithAuthDetails ||
    !props.companyUserWithAuthDetails.companyUser.authRefId
  ) {
    return;
  }

  const lockUnlockRequest: LockUnlockRequest = {
    company: sessionManager.getCompanyId(),
    authRefId: props.companyUserWithAuthDetails.companyUser.authRefId,
    roleRequests: [],
  };

  const roleRequest: LockUnlockRoleRequest = {
    division: currentRoleStatus.value.division,
    roleId: currentRoleStatus.value.roleId,
    locked:
      currentRoleStatus.value.status === UserRoleStatus.LOCKED ? false : true,
  };

  lockUnlockRequest.roleRequests.push(roleRequest);
  isLoadingRoleUpdateRequest.value = true;
  useWebsocketStore().sendWebsocketRequest(
    new WebSocketRequest(
      '/authUserDetails/lockUnlockRoles',
      lockUnlockRequest,
      true,
    ),
  );

  Mitt.on('lockUnlockRolesResponse', (response: LockUnlockRequest) => {
    if (!lockUnlockRequest) {
      showNotification('Something went wrong.', {
        title: 'Role Status Management',
        type: HealthLevel.ERROR,
      });
    } else {
      showNotification('The users Role was successfully updated.', {
        title: 'Role Status Management',
        type: HealthLevel.INFO,
      });

      if (
        props.companyUserWithAuthDetails &&
        props.companyUserWithAuthDetails.companyUser.authRefId ===
          response.authRefId
      ) {
        for (const roleUpdate of response.roleRequests) {
          const roleToUpdate: CompanyRoleStatus | undefined =
            props.companyUserWithAuthDetails.companyRoles.find(
              (x: CompanyRoleStatus) =>
                x.roleId === roleUpdate.roleId &&
                x.division === currentRoleStatus.value?.division,
            );

          if (roleToUpdate) {
            roleToUpdate.status = roleUpdate.locked
              ? UserRoleStatus.LOCKED
              : UserRoleStatus.ACTIVE;
          }
        }
      }
      closeRoleUpdateDialog();
    }
    Mitt.off('lockUnlockRolesResponse');
  });
}

/**
 * A computed ref that generates an error message if the user already has the selected role.
 * @type {ComputedRef<string>}
 */
const roleErrorMessage: ComputedRef<string> = computed(() => {
  if (
    !companyRoleStatus.value ||
    !props.companyUserWithAuthDetails ||
    props.companyUserWithAuthDetails.companyRoles.length === 0 ||
    isViewingExistingRole.value
  ) {
    return '';
  }
  return props.companyUserWithAuthDetails.companyRoles.find(
    (x: CompanyRoleStatus) =>
      x.division === companyRoleStatus.value?.division &&
      x.roleId === companyRoleStatus.value?.roleId,
  ) !== undefined
    ? 'User already has this role in the selected division.'
    : '';
});

/**
 * A computed ref that returns validation rules from the store.
 * @type {ComputedRef<ValidationRules>}
 */
const rules = computed(() => {
  return validationRules;
});

/**
 * A computed ref that checks if the current company user is new (without an ID).
 * @type {ComputedRef<boolean>}
 */
const isNewCompanyUser: ComputedRef<boolean> = computed(() => {
  if (!props.companyUserWithAuthDetails) {
    return false;
  }
  return !props.companyUserWithAuthDetails.companyUser._id;
});

/**
 * A computed ref that checks if the current user has read-only access.
 * @type {ComputedRef<boolean>}
 */
const isCurrentUserReadOnly: ComputedRef<boolean> = computed(() => {
  return isReadOnlyUser();
});

/**
 * A computed ref that checks if form fields should be disabled.
 * Form fields are disabled for read-only users when editing existing users.
 * @type {ComputedRef<boolean>}
 */
const areFormFieldsDisabled: ComputedRef<boolean> = computed(() => {
  if (isNewCompanyUser.value) {
    return false; // Allow editing when creating new users
  }
  return isCurrentUserReadOnly.value;
});

/**
 * A computed ref that provides user-friendly feedback about role management permissions.
 * @type {ComputedRef<string>}
 */
const roleManagementPermissionMessage: ComputedRef<string> = computed(() => {
  if (isCurrentUserReadOnly.value) {
    return 'You have read-only access and cannot manage user roles.';
  }

  if (hasAdminRole()) {
    return 'You can manage all user roles.';
  }

  if (hasAdminOrHeadOfficeOrBranchManagerRole()) {
    return 'You can manage the following roles: CSR, Team Leader.';
  }

  return 'You do not have permission to manage any roles.';
});

/**
 * A computed ref that returns role IDs that should be disabled in the role dropdown.
 * @type {ComputedRef<number[]>}
 */
const disabledRoleIds: ComputedRef<number[]> = computed(() => {
  if (hasAdminRole()) {
    return []; // Admin can manage all roles
  }

  if (isCurrentUserReadOnly.value) {
    return companyUserRoles.value.map((role) => role.roleId); // Disable all roles for read-only users
  }

  // For HEAD_OFFICE and BRANCH_MANAGER, disable all roles except CSR and TEAM_LEADER
  return companyUserRoles.value
    .filter(
      (role) =>
        role.roleType !== UserRoleType.ROLE_CSR &&
        role.roleType !== UserRoleType.ROLE_TEAM_LEADER,
    )
    .map((role) => role.roleId);
});

/**
 * Interface representing the confirmation details for a role removal operation.
 * @interface
 * @property {string} accountName - The name of the account associated with the role being removed.
 * @property {string} accountEmailAddress - The email address of the account associated with the role being removed.
 * @property {string} roleName - The name of the role to be removed.
 * @property {string} divisionName - The name of the division from which the role will be removed.
 */
interface RoleRemovalConfirmation {
  accountName: string;
  accountEmailAddress: string;
  roleName: string;
  divisionName: string;
}

/**
 * A computed ref that provides the details required for confirming the removal of a role from a user's account.
 * It dynamically constructs an object containing the user's account name, email address, the role name to be removed,
 * and the division name, based on the current state of `props.companyUserWithAuthDetails` and `companyRoleStatus`.
 *
 * @type {ComputedRef<RoleRemovalConfirmation>}
 * @returns {RoleRemovalConfirmation | null} The confirmation message details, including account name, account email address,
 *                                    role name, and division name, if the prerequisites are met. Returns null
 *                                    if the necessary details are not available.
 */
const roleRemovalConfirmationMessage: ComputedRef<RoleRemovalConfirmation | null> =
  computed(() => {
    if (!props.companyUserWithAuthDetails || !companyRoleStatus.value) {
      return null;
    }
    const roleId: number = companyRoleStatus.value.roleId;
    const userRole: UserRole | undefined = companyUserRoles.value.find(
      (x: UserRole) => x.roleId === roleId,
    );
    const roleName: string = userRole ? userRole.name : '';
    const division: KeyValue | undefined = props.companyDivisionList.find(
      (x: KeyValue) => x.value === companyRoleStatus.value!.division,
    );
    const divisionName: string = division ? division.key : '';
    const accountName: string =
      props.companyUserWithAuthDetails.companyUser.firstName +
      ' ' +
      props.companyUserWithAuthDetails.companyUser.lastName;
    const accountEmailAddress: string =
      props.companyUserWithAuthDetails.companyUser.emailAddress;
    return {
      accountName,
      accountEmailAddress,
      roleName,
      divisionName,
    };
  });

/**
 * Retrieves a list of disabled role IDs. We require this so the role Ids are disabled for
 * selection if they already exist on the user in role administration.
 *
 * @returns {number[]} An array of role IDs that are disabled for selection.
 */
const disabledRolesForSelectedDivision: ComputedRef<number[]> = computed(() => {
  if (!props.companyUserWithAuthDetails || !companyRoleStatus.value) {
    return [];
  }

  return props.companyUserWithAuthDetails.companyRoles
    .filter(
      (x: CompanyRoleStatus) =>
        x.division === companyRoleStatus.value!.division,
    )
    .map((x: CompanyRoleStatus) => x.roleId);
});

/**
 * Checks if the current user has read-only access to user management
 * CSR and TEAM_LEADER roles have read-only access when they don't have higher privileges
 */
function isReadOnlyUser(): boolean {
  const userRoles = sessionManager.getRoles();
  return (
    userRoles.some(
      (r) => r === UserRoleType.ROLE_CSR || r === UserRoleType.ROLE_TEAM_LEADER,
    ) &&
    !userRoles.some(
      (r) =>
        r === UserRoleType.ROLE_ADMIN ||
        r === UserRoleType.ROLE_HEAD_OFFICE ||
        r === UserRoleType.ROLE_BRANCH_MANAGER,
    )
  );
}

/**
 * Checks if the current user can manage a specific role type
 * @param roleType - The role type to check permissions for
 */
function canManageSpecificRole(roleType: UserRoleType): boolean {
  // Admin can manage all roles
  if (hasAdminRole()) {
    return true;
  }
  // Read-only users cannot manage any roles
  if (isReadOnlyUser()) {
    return false;
  }
  // HEAD_OFFICE and BRANCH_MANAGER can only manage CSR and TEAM_LEADER roles
  if (hasAdminOrHeadOfficeOrBranchManagerRole()) {
    return (
      roleType === UserRoleType.ROLE_CSR ||
      roleType === UserRoleType.ROLE_TEAM_LEADER
    );
  }
  return false;
}
</script>
<style scoped lang="scss">
.recipient-info {
  margin-bottom: 15px;
}
.recipient-info p {
  margin: 5px 0;
}

ul {
  list-style-type: none;
  padding: 0;
}
</style>
