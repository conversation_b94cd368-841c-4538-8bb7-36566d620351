<template>
  <div class="professional-file-upload" :class="{ 'upload-disabled': isUploadDisabled }">
    <!-- Header Section -->
    <div class="upload-header" v-if="imageLabel">
      <h3 class="upload-title">{{ imageLabel }}</h3>
      <div class="upload-info">
        <v-chip
          :color="attachmentSingle ? 'blue' : 'green'"
          small
          outlined
          class="mode-chip"
        >
          <v-icon left size="16">
            {{ attachmentSingle ? 'fas fa-file-outline' : 'fas fa-file-multiple-outline' }}
          </v-icon>
          {{ attachmentSingle ? 'Single File' : 'Multiple Files' }}
        </v-chip>
        <span class="file-count" v-if="!attachmentSingle && attachmentList.length > 0">
          {{ attachmentList.length }} file{{ attachmentList.length !== 1 ? 's' : '' }} uploaded
        </span>
      </div>
    </div>

    <!-- Progress Bar -->
    <v-progress-linear
      :active="showLoadingIndicator || awaitingImageDownload || isUploading"
      :indeterminate="true"
      color="primary"
      height="3"
      class="upload-progress"
      rounded
    ></v-progress-linear>

    <!-- Drop Zone -->
    <div
      class="drop-zone"
      :class="{
        'drop-zone-active': isDragOver,
        'drop-zone-disabled': isUploadDisabled,
        'has-files': attachmentList.length > 0
      }"
      @click="pickFile"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
      v-if="attachmentList.length === 0 || !attachmentSingle"
    >
      <div class="drop-zone-content">
        <div class="upload-icon-container">
          <v-icon
            :size="isUploading ? 40 : 48"
            :color="isUploadDisabled ? 'grey' : 'primary'"
            class="upload-main-icon"
            :class="{ 'rotating': isUploading }"
          >
            {{ isUploading ? 'fas fa-cloud-upload' : isDragOver ? 'fas fa-cloud-download' : 'fas fa-cloud-upload-outline' }}
          </v-icon>
        </div>
        <v-icon>fas fa-paperclip</v-icon>
        <div class="upload-text">
          <h4 class="upload-primary-text">
            {{
              isUploading ? 'Uploading files...' :
              isDragOver ? 'Drop files here' :
              'Drop files here or click to browse'
            }}
          </h4>
          <p class="upload-secondary-text" v-if="!isUploading">
            {{ getAcceptedTypesText() }}
          </p>
          <p class="upload-hint" v-if="!isUploading && !isDragOver">
            Maximum file size: 10MB
          </p>
        </div>
      </div>
    </div>

    <!-- Unified File Gallery Card -->
    <div class="unified-file-gallery" v-if="attachmentList.length > 0">
      <div class="gallery-header" v-if="!attachmentSingle">
        <h4 class="gallery-title">
          <v-icon left color="primary">fas fa-folder-multiple-image</v-icon>
          Uploaded Files ({{ attachmentList.length }})
        </h4>
      </div>

      <div class="gallery-grid">
        <div
          class="gallery-item"
          v-for="(attachmentItem, index) in attachmentList"
          :key="attachmentItem.id || index"
          @click="openGalleryViewer(index)"
        >
          <div class="gallery-item-overlay"></div>

          <!-- File Preview -->
          <div class="file-preview">
            <div class="file-preview-content">
              <!-- High-Quality Image Preview -->
              <div
                v-if="isImageFile(attachmentItem.mimeType) && (imageSrc || attachmentItem.data)"
                class="high-quality-image-container"
              >
                <img
                  :src="imageSrc || attachmentItem.data"
                  :alt="getDisplayFileName(attachmentItem.name)"
                  class="high-quality-image"
                  @load="onImageLoad"
                  @error="onImageError"
                />
                <div v-if="imageLoading" class="image-loading-overlay">
                  <v-progress-circular indeterminate color="primary" size="32"></v-progress-circular>
                </div>
              </div>

              <!-- Enhanced File Icon for Non-Images -->
              <div v-else class="enhanced-file-icon-container">
                <div class="icon-background" :class="getFileIconClass(attachmentItem.mimeType)">
                  <v-icon
                    :size="56"
                    color="white"
                    class="enhanced-file-icon"
                  >
                    {{ getFileIcon(attachmentItem.mimeType) }}
                  </v-icon>
                </div>
                <div class="file-type-badge">
                  {{ getFileTypeLabel(attachmentItem.mimeType) }}
                </div>
              </div>
            </div>

            <!-- File Overlay -->
            <div class="file-overlay">
              <v-btn
                icon
                small
                color="white"
                class="overlay-btn"
                @click.stop="viewFile(attachmentItem)"
              >
                <v-icon color="black">fas fa-eye</v-icon>
              </v-btn>
            </div>
          </div>

          <!-- File Info -->
          <div class="file-info">
            <div class="file-name" :title="getDisplayFileName(attachmentItem.name)">
              {{ getDisplayFileName(attachmentItem.name) }}
            </div>
            <div class="file-details">
              <span class="file-type">{{ getFileTypeLabel(attachmentItem.mimeType) }}</span>
              <span class="file-size" v-if="attachmentItem.size">{{ formatFileSize(attachmentItem.size) }}</span>
            </div>
          </div>

          <!-- File Actions -->
          <div class="file-actions">
            <v-btn
              icon
              small
              color="primary"
              @click.stop="viewFile(attachmentItem)"
              :disabled="awaitingImageDownload"
              title="View"
            >
              <v-icon size="18">fas fa-eye</v-icon>
            </v-btn>

            <v-btn
              icon
              small
              color="success"
              @click.stop="downloadFile(attachmentItem)"
              :disabled="awaitingImageDownload"
              title="Download"
            >
              <v-icon size="18">fas fa-download</v-icon>
            </v-btn>

            <v-btn
              v-if="!isAccountRecovery"
              icon
              small
              color="error"
              @click.stop="confirmDelete(attachmentItem)"
              :disabled="isUploadDisabled"
              title="Delete"
            >
              <v-icon size="18">fas fa-trash-alt</v-icon>
            </v-btn>

            <v-btn
              v-if="isAccountRecovery"
              icon
              small
              color="error"
              @click.stop="emit('setAttachment', null)"
              :disabled="formDisabled"
              title="Remove"
            >
              <v-icon size="18">fas fa-times</v-icon>
            </v-btn>
          </div>
        </div>
      </div>
    </div>

    <!-- Hidden File Input -->
    <input
      type="file"
      ref="fileInput"
      :id="'fileInput' + documentTypeId"
      style="display: none"
      :disabled="isUploadDisabled"
      :accept="acceptedFileTypes"
      :multiple="!attachmentSingle"
      @change="handleFileSelect"
    />

    <!-- Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400">
      <v-card>
        <v-card-title class="headline">
          <v-icon color="error" class="mr-2">fas fa-exclamation-circle</v-icon>
          Confirm Deletion
        </v-card-title>
        <v-card-text>
          Are you sure you want to remove "{{ fileToDelete?.name }}"? This action cannot be undone.
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn color="error" @click="deleteFile">Delete</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Unified File Viewer Dialog -->
    <v-dialog
      v-model="showViewerDialog"
      max-width="900"
      @keydown.esc="closeViewer"
      @keydown.left="previousFile"
      @keydown.right="nextFile"
    >
      <v-card class="dark-theme-card unified-viewer-card">
        <v-card-title class="d-flex align-center dark-theme-header">
          <v-icon class="mr-2" color="primary">fas fa-file</v-icon>
          {{ getDisplayFileName(currentViewingFile?.name || '') }}
          <v-spacer></v-spacer>
          <div class="file-counter" v-if="attachmentList.length > 1">
            {{ currentViewingIndex + 1 }} of {{ attachmentList.length }}
          </div>
          <v-btn icon @click="downloadFile(currentViewingFile)" class="ml-2" color="white">
            <v-icon>fas fa-download</v-icon>
          </v-btn>
          <v-btn icon @click="closeViewer" color="white">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="pa-0 dark-theme-content">
          <div class="unified-file-viewer">
            <!-- Navigation arrows for multiple files -->
            <div
              v-if="attachmentList.length > 1 && currentViewingIndex > 0"
              class="nav-arrow nav-arrow-left"
              @click="previousFile"
            >
              <v-btn icon large color="white">
                <v-icon size="32">fas fa-chevron-left</v-icon>
              </v-btn>
            </div>

            <div
              v-if="attachmentList.length > 1 && currentViewingIndex < attachmentList.length - 1"
              class="nav-arrow nav-arrow-right"
              @click="nextFile"
            >
              <v-btn icon large color="white">
                <v-icon size="32">fas fa-chevron-right</v-icon>
              </v-btn>
            </div>

            <!-- Main content area -->
            <div class="viewer-content">
              <div v-if="currentViewingFile && isImageFile(currentViewingFile.mimeType)" class="full-quality-image-container">
                <img
                  :src="getFullImageSrc(currentViewingFile)"
                  :alt="getDisplayFileName(currentViewingFile.name)"
                  class="full-quality-image"
                  @load="onImageLoad"
                  @error="onImageError"
                />
                <div v-if="imageLoading" class="image-loading-overlay">
                  <v-progress-circular indeterminate color="primary" size="48"></v-progress-circular>
                </div>
              </div>
              <div v-else class="non-image-viewer">
                <div class="icon-background" :class="getFileIconClass(currentViewingFile?.mimeType || '')">
                  <v-icon size="80" color="white">
                    {{ getFileIcon(currentViewingFile?.mimeType || '') }}
                  </v-icon>
                </div>
                <h4 class="mt-4 white--text">{{ getFileTypeLabel(currentViewingFile?.mimeType || '') }}</h4>
                <p class="text-caption grey--text">Preview not available for this file type</p>
              </div>
            </div>
          </div>
        </v-card-text>

        <!-- Thumbnail navigation for multiple files -->
        <div class="viewer-thumbnails" v-if="attachmentList.length > 1">
          <div class="thumbnail-scroll-container">
            <div
              v-for="(item, index) in attachmentList"
              :key="item.id || index"
              class="viewer-thumbnail"
              :class="{ 'active': index === currentViewingIndex }"
              @click="setCurrentViewingIndex(index)"
            >
              <div v-if="isImageFile(item.mimeType)" class="thumbnail-image-wrapper">
                <img :src="item.data" :alt="getDisplayFileName(item.name)" class="thumbnail-image" />
              </div>
              <div v-else class="thumbnail-icon-wrapper">
                <v-icon color="white" size="20">{{ getFileIcon(item.mimeType) }}</v-icon>
              </div>
              <div class="thumbnail-overlay">
                <span class="thumbnail-index">{{ index + 1 }}</span>
              </div>
            </div>
          </div>
        </div>

        <v-card-actions class="dark-theme-actions">
          <v-btn color="primary" @click="downloadFile(currentViewingFile)">
            <v-icon left>fas fa-download</v-icon>
            Download
          </v-btn>
          <v-spacer></v-spacer>
          <v-btn text color="white" @click="closeViewer">Close</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  downloadAttachment,
  validMimeTypes
} from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { Attachment } from '@/interface-models/Generic/Attachment/Attachment';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { useAttachmentStore } from '@/store/modules/AttachmentStore';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { computed, ComputedRef, onMounted, Ref, ref } from 'vue';

const props = withDefaults(
  defineProps<{
    documentTypeId: number;
    attachmentArray?: Attachment[];
    attachment?: Attachment | null;
    attachmentSingle: boolean;
    imageLabel: string;
    formDisabled?: boolean;
    isPDF?: boolean;
    hideIcon?: boolean;
    isLoading?: boolean;
    // isAccountRecovery will emit the attachment back to parent instead of sending a save request.
    isAccountRecovery?: boolean;
  }>(),
  {
    attachment: null,
    isPDF: false,
    hideIcon: false,
    formDisabled: false,
    isLoading: false,
    isAccountRecovery: false,
    attachmentArray: () => [],
  },
);

const awaitingImageSaveFileName: Ref<string | null> = ref(null);
const awaitingImageDownload: Ref<boolean> = ref(false);
const imageSrc: Ref<string | null> = ref(null);
const fileInput = ref<HTMLInputElement | null>(null);
const isUploading: Ref<boolean> = ref(false);
const isDragOver: Ref<boolean> = ref(false);
const showDeleteDialog: Ref<boolean> = ref(false);
const showViewerDialog: Ref<boolean> = ref(false);
const fileToDelete: Ref<Attachment | null> = ref(null);
const viewingFile: Ref<Attachment | null> = ref(null);
const currentViewingIndex: Ref<number> = ref(0);
const imageLoading: Ref<boolean> = ref(false);

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'resetAttachmentData'): void;
  (event: 'setAttachment', attachment: Attachment | null): void;
}>();

const showLoadingIndicator: ComputedRef<boolean> = computed(() => {
  return (
    props.isLoading &&
    awaitingImageSaveFileName.value !== null &&
    awaitingImageSaveFileName.value !== ''
  );
});

const isUploadDisabled: ComputedRef<boolean> = computed(() => {
  return props.formDisabled || isUploading.value || showLoadingIndicator.value;
});

const acceptedFileTypes: ComputedRef<string> = computed(() => {
  return validMimeTypes.join(',');
});

const attachmentList: ComputedRef<Attachment[]> = computed(() => {
  if (props.attachmentSingle) {
    if (props.attachment && (props.attachment.id || props.isAccountRecovery)) {
      return [props.attachment];
    } else {
      return [];
    }
  } else {
    return props.attachmentArray.filter(
      (x: Attachment) => x.documentTypeId === props.documentTypeId,
    );
  }
});



const currentViewingFile: ComputedRef<Attachment | null> = computed(() => {
  return attachmentList.value[currentViewingIndex.value] || null;
});

// Helper function to get display filename (remove UUID prefix)
function getDisplayFileName(filename: string): string {
  if (!filename) {return 'Untitled';}
  // Remove UUID prefix (36 chars + hyphen) if present
  const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}-/i;
  return filename.replace(uuidPattern, '');
}

// Helper function to get file type label
function getFileTypeLabel(mimeType: string): string {
  const typeMap: Record<string, string> = {
    'application/pdf': 'PDF',
    'text/csv': 'CSV',
    'text/plain': 'Text',
    'application/json': 'JSON',
    'application/xml': 'XML',
    'text/xml': 'XML',
    'application/msword': 'Word',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel'
  };

  if (typeMap[mimeType]) {return typeMap[mimeType];}
  if (mimeType.startsWith('image/')) {return 'Image';}
  return 'File';
}

// Helper function to get file icon
function getFileIcon(mimeType: string): string {
  if (mimeType === 'application/pdf') {return 'mdi-file-pdf-box';}
  if (mimeType.startsWith('image/')) {return 'mdi-file-image';}
  if (mimeType === 'text/csv' || mimeType.includes('spreadsheet')) {return 'mdi-file-excel';}
  if (mimeType.includes('wordprocessing') || mimeType === 'application/msword') {return 'mdi-file-word';}
  if (mimeType === 'text/plain') {return 'mdi-file-document';}
  if (mimeType === 'application/json' || mimeType.includes('xml')) {return 'mdi-file-code';}
  return 'mdi-file';
}

// Helper function to get file icon class for background styling
function getFileIconClass(mimeType: string): string {
  if (mimeType === 'application/pdf') {return 'pdf-icon';}
  if (mimeType.startsWith('image/')) {return 'image-icon';}
  if (mimeType === 'text/csv' || mimeType.includes('spreadsheet')) {return 'excel-icon';}
  if (mimeType.includes('wordprocessing') || mimeType === 'application/msword') {return 'word-icon';}
  if (mimeType === 'text/plain') {return 'text-icon';}
  if (mimeType === 'application/json' || mimeType.includes('xml')) {return 'code-icon';}
  return 'default-icon';
}

// Helper function to check if file is an image
function isImageFile(mimeType: string): boolean {
  return mimeType.startsWith('image/') && !mimeType.includes('heic') && !mimeType.includes('heif');
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) {return '0 Bytes';}
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Helper function to get accepted file types text
function getAcceptedTypesText(): string {
  const types = ['Images', 'PDFs', 'Documents', 'Spreadsheets'];
  return `Supported: ${types.join(', ')}`;
}

async function getFull(documentId: string, download: boolean) {
  // Early return if the function is called during account recovery or without
  // a document ID or if the user is already waiting for the download response
  if (props.isAccountRecovery || !documentId || awaitingImageDownload.value) {
    if (!documentId) {
      showNotification(GENERIC_ERROR_MESSAGE, { title: 'File Upload' });
    }
    return;
  }

  awaitingImageDownload.value = true;
  try {
    const fullAttachment =
      await useAttachmentStore().getAttachmentById(documentId);

    // If the attachment is found, attempt to download it
    if (fullAttachment) {
      if (download) {
        const downloadSuccess = downloadAttachment(fullAttachment);
        // If the download fails, show an error notification to the user
        if (!downloadSuccess) {
          showNotification(GENERIC_ERROR_MESSAGE, { title: 'File Download' });
        }
      }
      return fullAttachment.data;
    } else if (!fullAttachment) {
      showNotification(
        'Something went wrong. The attachment could not be found.',
        { title: 'File Upload' },
      );
    }
  } finally {
    awaitingImageDownload.value = false;
  }
}

// Helper function to get the full image URL
async function loadImageSrc(attachment: Attachment) {
  try {
    if (
      attachment &&
      attachment.mimeType &&
      attachment.mimeType.includes('image') &&
      !attachment.mimeType.includes('heic') &&
      !attachment.mimeType.includes('heif')
    ) {
      const imageUrl = await displayAttachmentImage(attachment.id);
      imageSrc.value = imageUrl ? imageUrl : null;
    } else {
      imageSrc.value = null;
    }
  } catch (error) {
    console.error('Error loading image source: ', error);
    imageSrc.value = null;
  }
}

async function displayAttachmentImage(documentId: string) {
  try {
    const fullAttachment = await getFull(documentId, false);
    if (fullAttachment) {
      return fullAttachment;
    }
    return null;
  } catch (error) {
    console.error('Error fetching attachment: ', error);
    return null;
  }
}

function deleteAttachment(documentId: string): void {
  if (!fileInput.value) {
    (fileInput as any).value = '';
  }

  if (!props.attachmentSingle) {
    const index = props.attachmentArray.findIndex(
      (item: any) => item.id === documentId,
    );
    props.attachmentArray.splice(index, 1);
  }
  if (props.attachmentSingle && props.attachment) {
    props.attachment.id = '';
    props.attachment.documentTypeId = props.documentTypeId;
    props.attachment.mimeType = '';
    props.attachment.data = '';
    props.attachment.name = '';
    props.attachment.timestamp = moment().valueOf();
    props.attachment.approvalStatus = [];
  }
}

// Drag and drop handlers
function handleDragOver(event: DragEvent) {
  if (isUploadDisabled.value) {return;}
  event.preventDefault();
  isDragOver.value = true;
}

function handleDragLeave(event: DragEvent) {
  event.preventDefault();
  isDragOver.value = false;
}

function handleDrop(event: DragEvent) {
  event.preventDefault();
  isDragOver.value = false;

  if (isUploadDisabled.value) {return;}

  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    processFiles(files);
  }
}

// File selection handlers
function pickFile() {
  if (isUploadDisabled.value) {return;}
  const fileInputElement = fileInput.value;
  if (fileInputElement) {
    fileInputElement.click();
  }
}

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  if (files && files.length > 0) {
    processFiles(files);
  }
}

// File processing
function processFiles(files: FileList) {
  const fileArray = Array.from(files);

  // Validate file count for single file mode
  if (props.attachmentSingle && fileArray.length > 1) {
    showNotification('Only one file is allowed in single file mode.', {
      type: HealthLevel.ERROR,
    });
    return;
  }

  // Process each file
  fileArray.forEach(file => {
    if (validateFile(file)) {
      uploadFile(file);
    }
  });
}

function validateFile(file: File): boolean {
  // Check file type
  if (!validMimeTypes.includes(file.type)) {
    showNotification(`File type "${file.type}" is not supported.`, {
      type: HealthLevel.ERROR,
    });
    return false;
  }

  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    showNotification(`File "${file.name}" is too large. Maximum size is 10MB.`, {
      type: HealthLevel.ERROR,
    });
    return false;
  }

  return true;
}

// Image loading handlers
function onImageLoad() {
  imageLoading.value = false;
}

function onImageError() {
  imageLoading.value = false;
  console.error('Failed to load image');
}

// Helper function to get full image source for viewer
function getFullImageSrc(attachment: Attachment): string {
  // For images, return the full data or load it if needed
  if (attachment.data) {
    return attachment.data;
  }
  // If no data, try to load it
  if (attachment.id && isImageFile(attachment.mimeType)) {
    loadImageSrc(attachment);
  }
  return '';
}

// Unified viewer navigation methods
function openGalleryViewer(index: number) {
  currentViewingIndex.value = index;
  viewingFile.value = attachmentList.value[index];
  showViewerDialog.value = true;

  // Load full image data if needed
  const file = attachmentList.value[index];
  if (file && file.id && isImageFile(file.mimeType)) {
    loadImageSrc(file);
  }
}

function closeViewer() {
  showViewerDialog.value = false;
  currentViewingIndex.value = 0;
  viewingFile.value = null;
}

function nextFile() {
  if (currentViewingIndex.value < attachmentList.value.length - 1) {
    currentViewingIndex.value++;
    viewingFile.value = attachmentList.value[currentViewingIndex.value];

    // Load full image data if needed
    const file = attachmentList.value[currentViewingIndex.value];
    if (file && file.id && isImageFile(file.mimeType)) {
      loadImageSrc(file);
    }
  }
}

function previousFile() {
  if (currentViewingIndex.value > 0) {
    currentViewingIndex.value--;
    viewingFile.value = attachmentList.value[currentViewingIndex.value];

    // Load full image data if needed
    const file = attachmentList.value[currentViewingIndex.value];
    if (file && file.id && isImageFile(file.mimeType)) {
      loadImageSrc(file);
    }
  }
}

function setCurrentViewingIndex(index: number) {
  currentViewingIndex.value = index;
  viewingFile.value = attachmentList.value[index];

  // Load full image data if needed
  const file = attachmentList.value[index];
  if (file && file.id && isImageFile(file.mimeType)) {
    loadImageSrc(file);
  }
}

// File operations
function viewFile(attachment: Attachment) {
  if (!attachment) {return;}

  // Find the index of the attachment in the list
  const index = attachmentList.value.findIndex(item => item.id === attachment.id);
  if (index >= 0) {
    openGalleryViewer(index);
  } else {
    // Fallback for single file view
    currentViewingIndex.value = 0;
    viewingFile.value = attachment;
    showViewerDialog.value = true;

    // Load full image data if needed
    if (attachment.id && isImageFile(attachment.mimeType)) {
      loadImageSrc(attachment);
    }
  }
}

function downloadFile(attachment: Attachment | null) {
  if (!attachment?.id) {return;}
  getFull(attachment.id, true);
}

function confirmDelete(attachment: Attachment) {
  fileToDelete.value = attachment;
  showDeleteDialog.value = true;
}

function deleteFile() {
  if (fileToDelete.value?.id) {
    deleteAttachment(fileToDelete.value.id);
  }
  showDeleteDialog.value = false;
  fileToDelete.value = null;
}

// File upload method
function uploadFile(file: File) {
  try {
    isUploading.value = true;
    emit('update:isLoading', true);

    const attachment: Attachment = new Attachment();
    attachment.documentTypeId = props.documentTypeId;

    const promise = getBase64(file);
    if (!promise) {
      resetComponent(true);
      return;
    }

    promise
      .then((result) => {
        const base64: any = result;
        const guid = uuidv4();
        const fileName: string = guid + '-' + file.name;
        attachment.data = base64;
        attachment.mimeType = file.type;
        attachment.name = fileName;
        attachment.size = file.size; // Add file size
        awaitingImageSaveFileName.value = guid.toLowerCase();

        if (props.isAccountRecovery) {
          emit('setAttachment', attachment);
        } else {
          saveAttachment(attachment);
        }
      })
      .catch((error) => {
        console.error('Error processing file:', error);
        resetComponent(true);
      });
  } catch (error) {
    console.error(error);
    resetComponent(true);
  }
}

function getBase64(file: File): Promise<string> | null {
  try {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        resolve(reader.result as string);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  } catch (error) {
    console.error(error);
    return null;
  }
}



/**
 * Dispatches the save operation for the new Attachment document, and handles
 * the response
 * @param attachment Attachment object to be saved
 */
async function saveAttachment(attachment: Attachment) {
  const savedAttachment = await useAttachmentStore().saveAttachment(attachment);
  setSavedAttachment(savedAttachment);
}

/**
 * Handles response to a newly saved Attachment document
 * @param attachment the response to the save operation
 */
function setSavedAttachment(attachment: Attachment | null) {
  if (!awaitingImageSaveFileName.value) {
    return;
  }
  if (
    !attachment ||
    !attachment.name ||
    !attachment.name.toLowerCase().includes(awaitingImageSaveFileName.value)
  ) {
    showNotification(GENERIC_ERROR_MESSAGE, { title: 'File Upload' });
    isUploading.value = false;
    emit('update:isLoading', false);
    awaitingImageSaveFileName.value = null;
    return;
  }
  if (props.attachmentSingle && props.attachment) {
    props.attachment.id = attachment.id;
    props.attachment.documentTypeId = attachment.documentTypeId;
    props.attachment.mimeType = attachment.mimeType;
    props.attachment.data = attachment.data;
    props.attachment.name = attachment.name;
    props.attachment.timestamp = attachment.timestamp;
  } else {
    props.attachmentArray.push(attachment);
  }
  isUploading.value = false;
  emit('update:isLoading', false);
  awaitingImageSaveFileName.value = null;
}

function resetComponent(error: boolean) {
  if (error) {
    showNotification(GENERIC_ERROR_MESSAGE, { title: 'File Upload' });
  }
  isUploading.value = false;
  awaitingImageSaveFileName.value = null;
  emit('update:isLoading', false);
}

onMounted(() => {
  // Load the image source when the component is mounted
  if (props.attachment) {
    loadImageSrc(props.attachment);
  }
});
</script>

<style scoped lang="scss">
.professional-file-upload {
  background: #1a1a1a;
  color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  &.upload-disabled {
    pointer-events: none;
    opacity: 0.6;
  }
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 20px;
  background: linear-gradient(135deg, #2d2d2d 0%, #1f1f1f 100%);
  border-radius: 12px;
  border: 1px solid #404040;

  .upload-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .upload-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .mode-chip {
      font-weight: 500;
      background: rgba(33, 150, 243, 0.2) !important;
      border-color: #2196f3 !important;
      color: #64b5f6 !important;
    }

    .file-count {
      font-size: 0.875rem;
      color: #b0b0b0;
      font-weight: 500;
    }
  }
}

.upload-progress {
  border-radius: 4px;
  overflow: hidden;
}

.drop-zone {
  border: 2px dashed #555555;
  border-radius: 16px;
  padding: 8px 22px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(33, 150, 243, 0.1), transparent);
    transition: left 0.5s;
  }

  &:hover:not(.drop-zone-disabled) {
    border-color: #2196f3;
    background: linear-gradient(135deg, #1e3a5f 0%, #1a2332 100%);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(33, 150, 243, 0.25);

    &::before {
      left: 100%;
    }

    .upload-main-icon {
      transform: scale(1.15);
      color: #64b5f6 !important;
    }
  }

  &.drop-zone-active {
    border-color: #4caf50;
    background: linear-gradient(135deg, #1b4332 0%, #2d5a3d 100%);
    transform: scale(1.03);
    box-shadow: 0 16px 40px rgba(76, 175, 80, 0.3);

    .upload-main-icon {
      color: #81c784 !important;
      transform: scale(1.25);
    }
  }

  &.drop-zone-disabled {
    cursor: not-allowed;
    opacity: 0.4;
    filter: grayscale(1);
  }
}

.drop-zone-content {
  position: relative;
  z-index: 1;
}

.upload-icon-container {
  .upload-main-icon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    &.rotating {
      animation: rotate 2s linear infinite;
    }
  }
}

.upload-text {
  .upload-primary-text {
    margin: 0 0 12px 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .upload-secondary-text {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    color: #b0b0b0;
  }

  .upload-hint {
    margin: 0;
    font-size: 0.8rem;
    color: #888888;
  }
}

// Unified File Gallery - Dark Theme
.unified-file-gallery {
  background: linear-gradient(135deg, #2d2d2d 0%, #1f1f1f 100%);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #404040;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.gallery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #404040;

  .gallery-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    display: flex;
    align-items: center;
  }

  .gallery-actions {
    .v-btn {
      border-color: #2196f3 !important;
      color: #64b5f6 !important;

      &:hover {
        background: rgba(33, 150, 243, 0.1) !important;
        transform: translateY(-1px);
      }
    }
  }
}

.gallery-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.gallery-item {
  background: linear-gradient(135deg, #3a3a3a 0%, #2a2a2a 100%);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #505050;
  cursor: pointer;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  min-height: 100px;

  &:hover {
    transform: translateX(4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    border-color: #2196f3;

    .file-overlay {
      opacity: 1;
    }

    .file-actions {
      transform: translateY(0);
      opacity: 1;
    }

    .gallery-item-overlay {
      opacity: 1;
    }
  }
}

.gallery-item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(33, 150, 243, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

// High-Quality Image Container - Horizontal Layout
.high-quality-image-container {
  position: relative;
  width: 120px;
  height: 80px;
  overflow: hidden;
  border-radius: 8px;
  flex-shrink: 0;

  .high-quality-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;

    &:hover {
      transform: scale(1.05);
    }
  }

  .image-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// Enhanced File Icon Container - Horizontal Layout
.enhanced-file-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 80px;
  background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
  position: relative;
  flex-shrink: 0;

  .icon-background {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 6px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;

    &.pdf-icon {
      background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
    }

    &.image-icon {
      background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    }

    &.excel-icon {
      background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    }

    &.word-icon {
      background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    }

    &.text-icon {
      background: linear-gradient(135deg, #757575 0%, #424242 100%);
    }

    &.code-icon {
      background: linear-gradient(135deg, #ff9800 0%, #e65100 100%);
    }

    &.default-icon {
      background: linear-gradient(135deg, #9e9e9e 0%, #616161 100%);
    }

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    }
  }

  .enhanced-file-icon {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    font-size: 32px !important;
  }

  .file-type-badge {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.6rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

.file-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;

  .overlay-btn {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(4px);

    &:hover {
      background: white !important;
      transform: scale(1.1);
    }
  }
}

.file-info {
  padding: 12px 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .file-name {
    font-weight: 600;
    font-size: 0.9rem;
    color: #ffffff;
    margin-bottom: 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .file-details {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .file-type {
      font-size: 0.75rem;
      color: #64b5f6;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      background: rgba(33, 150, 243, 0.1);
      padding: 2px 8px;
      border-radius: 8px;
      border: 1px solid rgba(33, 150, 243, 0.3);
    }

    .file-size {
      font-size: 0.75rem;
      color: #b0b0b0;
      font-weight: 500;
    }
  }
}

.file-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #1f1f1f 0%, #0f0f0f 100%);
  border-left: 1px solid #404040;
  transform: translateX(10px);
  opacity: 0;
  transition: all 0.3s ease;
  flex-shrink: 0;

  .v-btn {
    min-width: 32px;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(4px);

    &:hover {
      transform: scale(1.15);
      background: rgba(255, 255, 255, 0.2) !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    &.v-btn--disabled {
      opacity: 0.3;
    }
  }
}



// Unified Viewer Dialog Styles
.unified-viewer-card {
  .file-counter {
    font-size: 0.875rem;
    color: #b0b0b0;
    font-weight: 500;
    margin-right: 16px;
  }
}

.unified-file-viewer {
  position: relative;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1a1a1a;
}

.nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  cursor: pointer;

  &.nav-arrow-left {
    left: 20px;
  }

  &.nav-arrow-right {
    right: 20px;
  }

  .v-btn {
    background: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(4px);

    &:hover {
      background: rgba(0, 0, 0, 0.9) !important;
      transform: scale(1.1);
    }
  }
}

.viewer-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.full-quality-image-container {
  position: relative;
  max-width: 100%;
  max-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;

  .full-quality-image {
    max-width: 100%;
    max-height: 500px;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  .image-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
  }
}

.non-image-viewer {
  text-align: center;
  padding: 60px;

  .icon-background {
    width: 120px;
    height: 120px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  }
}

.viewer-thumbnails {
  background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
  border-top: 1px solid #404040;
  padding: 12px;
}

.thumbnail-scroll-container {
  display: flex;
  justify-content: center;
  gap: 8px;
  overflow-x: auto;
  padding: 4px 0;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.viewer-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  flex-shrink: 0;

  &.active {
    border-color: #2196f3;
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(33, 150, 243, 0.4);
  }

  &:hover:not(.active) {
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
  }

  .thumbnail-image-wrapper,
  .thumbnail-icon-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #333;
  }

  .thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .thumbnail-overlay {
    position: absolute;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 0.7rem;
    padding: 2px 4px;
    border-radius: 4px 0 0 0;
  }
}

.dark-theme-card {
  background: linear-gradient(135deg, #2d2d2d 0%, #1f1f1f 100%) !important;
  border: 1px solid #404040 !important;
}

.dark-theme-header {
  background: linear-gradient(135deg, #3a3a3a 0%, #2a2a2a 100%) !important;
  border-bottom: 1px solid #404040 !important;
  color: #ffffff !important;
}

.dark-theme-content {
  background: #1a1a1a !important;
}

.dark-theme-actions {
  background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%) !important;
  border-top: 1px solid #404040 !important;
}



// Animations
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}



// Enhanced Animations and Transitions
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.gallery-item {
  animation: slideInUp 0.3s ease-out;
}

// Enhanced Responsive Design for Horizontal Layout
@media (max-width: 768px) {
  .professional-file-upload {
    padding: 16px;
  }

  .gallery-item {
    min-height: 80px;

    .high-quality-image-container,
    .enhanced-file-icon-container {
      width: 100px;
      height: 60px;
    }

    .enhanced-file-icon-container .icon-background {
      width: 40px;
      height: 40px;
    }

    .file-info {
      padding: 8px 12px;

      .file-name {
        font-size: 0.8rem;
      }
    }

    .file-actions {
      padding: 6px 8px;

      .v-btn {
        width: 28px;
        height: 28px;
        min-width: 28px;
      }
    }
  }

  .upload-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
  }

  .drop-zone {
    padding: 32px 20px;
  }

  .unified-file-gallery {
    padding: 16px;
  }

  .unified-file-viewer {
    min-height: 400px;

    .nav-arrow {
      &.nav-arrow-left { left: 10px; }
      &.nav-arrow-right { right: 10px; }
    }
  }

  .viewer-thumbnails {
    padding: 8px;
  }

  .viewer-thumbnail {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .gallery-item {
    min-height: 70px;

    .high-quality-image-container,
    .enhanced-file-icon-container {
      width: 80px;
      height: 50px;
    }

    .enhanced-file-icon-container .icon-background {
      width: 35px;
      height: 35px;
    }

    .file-info {
      padding: 6px 10px;

      .file-name {
        font-size: 0.75rem;
      }

      .file-details {
        .file-type {
          font-size: 0.65rem;
          padding: 1px 6px;
        }

        .file-size {
          font-size: 0.65rem;
        }
      }
    }

    .file-actions {
      padding: 4px 6px;

      .v-btn {
        width: 24px;
        height: 24px;
        min-width: 24px;
      }
    }
  }

  .drop-zone {
    padding: 24px 16px;
  }

  .upload-text .upload-primary-text {
    font-size: 1.1rem;
  }

  .unified-file-viewer {
    min-height: 300px;

    .viewer-content {
      padding: 10px;
    }

    .full-quality-image-container .full-quality-image {
      max-height: 300px;
    }
  }

  .viewer-thumbnail {
    width: 40px;
    height: 40px;
  }
}

// Accessibility Enhancements
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// High Contrast Mode Support
@media (prefers-contrast: high) {
  .professional-file-upload {
    border: 2px solid #ffffff;
  }

  .drop-zone {
    border-width: 3px;
  }

  .gallery-item {
    border-width: 2px;
  }
}

// Print Styles
@media print {
  .professional-file-upload {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  .gallery-nav-btn,
  .file-actions,
  .browse-btn {
    display: none !important;
  }
}
</style>
</style>
